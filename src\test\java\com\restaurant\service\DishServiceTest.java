package com.restaurant.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import com.restaurant.model.Dish;

/**
 * 菜品服务测试类
 * Dish Service Test
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DishServiceTest {
    
    private DishService dishService;
    
    @BeforeEach
    void setUp() {
        dishService = new DishService();
    }
    
    @Test
    void testGetAllDishes() {
        List<Dish> dishes = dishService.getAllDishes();
        assertNotNull(dishes);
        assertFalse(dishes.isEmpty());
    }
    
    @Test
    void testGetAvailableDishes() {
        List<Dish> availableDishes = dishService.getAvailableDishes();
        assertNotNull(availableDishes);
        
        // 验证所有返回的菜品都是可用的
        for (Dish dish : availableDishes) {
            assertTrue(dish.isAvailable());
        }
    }
    
    @Test
    void testFindDishById() {
        // 测试查找存在的菜品
        Optional<Dish> dish = dishService.findDishById("001");
        assertTrue(dish.isPresent());
        assertEquals("001", dish.get().getId());
        
        // 测试查找不存在的菜品
        Optional<Dish> nonExistentDish = dishService.findDishById("999");
        assertFalse(nonExistentDish.isPresent());
    }
    
    @Test
    void testSearchDishesByName() {
        List<Dish> dishes = dishService.searchDishesByName("鸡丁");
        assertNotNull(dishes);
        
        // 验证搜索结果包含关键词
        for (Dish dish : dishes) {
            assertTrue(dish.getName().contains("鸡丁"));
        }
    }
    
    @Test
    void testAddDish() {
        Dish newDish = new Dish("TEST001", "测试菜品", new BigDecimal("20.00"), "测试分类");
        boolean result = dishService.addDish(newDish);
        assertTrue(result);
        
        // 验证菜品已添加
        Optional<Dish> addedDish = dishService.findDishById("TEST001");
        assertTrue(addedDish.isPresent());
        assertEquals("测试菜品", addedDish.get().getName());
        
        // 清理测试数据
        dishService.deleteDish("TEST001");
    }
    
    @Test
    void testValidateDish() {
        // 测试有效菜品
        Dish validDish = new Dish("VALID001", "有效菜品", new BigDecimal("15.00"), "测试分类");
        String validationResult = dishService.validateDish(validDish);
        assertNull(validationResult);
        
        // 测试无效菜品 - 空名称
        Dish invalidDish = new Dish("INVALID001", "", new BigDecimal("15.00"), "测试分类");
        String invalidResult = dishService.validateDish(invalidDish);
        assertNotNull(invalidResult);
        assertTrue(invalidResult.contains("名称不能为空"));
        
        // 测试无效菜品 - 负价格
        Dish negativePrice = new Dish("INVALID002", "无效菜品", new BigDecimal("-5.00"), "测试分类");
        String negativeResult = dishService.validateDish(negativePrice);
        assertNotNull(negativeResult);
        assertTrue(negativeResult.contains("价格必须大于0"));
    }
    
    @Test
    void testSetDishDiscount() {
        // 先添加一个测试菜品
        Dish testDish = new Dish("DISCOUNT001", "折扣测试菜品", new BigDecimal("30.00"), "测试分类");
        dishService.addDish(testDish);
        
        // 设置折扣
        boolean result = dishService.setDishDiscount("DISCOUNT001", new BigDecimal("0.8"));
        assertTrue(result);
        
        // 验证折扣已设置
        Optional<Dish> updatedDish = dishService.findDishById("DISCOUNT001");
        assertTrue(updatedDish.isPresent());
        assertEquals(0, new BigDecimal("0.8").compareTo(updatedDish.get().getDiscount()));
        assertEquals(0, new BigDecimal("24.00").compareTo(updatedDish.get().getDiscountedPrice()));
        
        // 清理测试数据
        dishService.deleteDish("DISCOUNT001");
    }
    
    @Test
    void testGetAllCategories() {
        List<String> categories = dishService.getAllCategories();
        assertNotNull(categories);
        assertFalse(categories.isEmpty());
        
        // 验证包含默认分类
        assertTrue(categories.contains("主食"));
        assertTrue(categories.contains("汤类"));
        assertTrue(categories.contains("素食"));
        assertTrue(categories.contains("饮品"));
    }
}
