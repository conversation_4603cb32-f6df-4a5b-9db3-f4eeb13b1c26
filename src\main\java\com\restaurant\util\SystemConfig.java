package com.restaurant.util;

import java.math.BigDecimal;

/**
 * 系统配置类
 */
public class SystemConfig {
    
    // 系统信息
    public static final String SYSTEM_NAME = "餐厅自助点餐系统";
    public static final String SYSTEM_VERSION = "1.0.0";
    public static final String SYSTEM_AUTHOR = "Restaurant System";
    
    // 默认配置
    public static final BigDecimal DEFAULT_DELIVERY_FEE = new BigDecimal("5.00");
    public static final BigDecimal DEFAULT_PRIVATE_ROOM_FEE = new BigDecimal("20.00");
    
    // 文件配置
    public static final String DATA_ENCODING = "UTF-8";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    // UI配置
    public static final int MAIN_WINDOW_WIDTH = 1000;
    public static final int MAIN_WINDOW_HEIGHT = 700;
    public static final String DEFAULT_FONT_NAME = "微软雅黑";
    public static final int DEFAULT_FONT_SIZE = 12;
    
    // 业务规则
    public static final int MAX_ORDER_ITEMS = 50;
    public static final int MAX_QUANTITY_PER_ITEM = 99;
    public static final BigDecimal MIN_DISCOUNT = new BigDecimal("0.1");
    public static final BigDecimal MAX_DISCOUNT = new BigDecimal("1.0");
    
    // 验证规则
    public static final int MIN_USERNAME_LENGTH = 3;
    public static final int MAX_USERNAME_LENGTH = 20;
    public static final int MIN_PASSWORD_LENGTH = 6;
    public static final int MAX_PASSWORD_LENGTH = 20;
    public static final String PHONE_PATTERN = "^1[3-9]\\d{9}$";
    public static final String EMAIL_PATTERN = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
    
    // 错误消息
    public static final String ERROR_INVALID_INPUT = "输入数据无效";
    public static final String ERROR_NETWORK = "网络连接错误";
    public static final String ERROR_FILE_IO = "文件读写错误";
    public static final String ERROR_DATA_FORMAT = "数据格式错误";
    
    // 成功消息
    public static final String SUCCESS_SAVE = "保存成功";
    public static final String SUCCESS_DELETE = "删除成功";
    public static final String SUCCESS_UPDATE = "更新成功";
    public static final String SUCCESS_LOGIN = "登录成功";
    
    /**
     * 获取系统完整名称
     */
    public static String getFullSystemName() {
        return SYSTEM_NAME + " v" + SYSTEM_VERSION;
    }
    
    /**
     * 获取版权信息
     */
    public static String getCopyright() {
        return "© 2024 " + SYSTEM_AUTHOR + " " + SYSTEM_VERSION;
    }
    
    /**
     * 验证手机号格式
     */
    public static boolean isValidPhone(String phone) {
        return phone != null && phone.matches(PHONE_PATTERN);
    }
    
    /**
     * 验证邮箱格式
     */
    public static boolean isValidEmail(String email) {
        return email != null && email.matches(EMAIL_PATTERN);
    }
    
    /**
     * 验证折扣范围
     */
    public static boolean isValidDiscount(BigDecimal discount) {
        return discount != null && 
               discount.compareTo(MIN_DISCOUNT) >= 0 && 
               discount.compareTo(MAX_DISCOUNT) <= 0;
    }
    
    /**
     * 验证用户名长度
     */
    public static boolean isValidUsernameLength(String username) {
        return username != null && 
               username.length() >= MIN_USERNAME_LENGTH && 
               username.length() <= MAX_USERNAME_LENGTH;
    }
    
    /**
     * 验证密码长度
     */
    public static boolean isValidPasswordLength(String password) {
        return password != null && 
               password.length() >= MIN_PASSWORD_LENGTH && 
               password.length() <= MAX_PASSWORD_LENGTH;
    }
}
