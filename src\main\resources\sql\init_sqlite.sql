-- 餐厅自助点餐系统数据库初始化脚本 (SQLite)
-- Restaurant Self-Ordering System Database Initialization Script (SQLite)

-- 管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    email TEXT,
    active INTEGER DEFAULT 1,
    last_login_time TEXT,
    created_time TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_time TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 菜品表
CREATE TABLE IF NOT EXISTS dish (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    price REAL NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    available INTEGER DEFAULT 1,
    discount REAL DEFAULT 1.0,
    created_time TEXT DEFAULT CURRENT_TIMESTAMP,
    updated_time TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id TEXT PRIMARY KEY,
    order_type TEXT NOT NULL CHECK(order_type IN ('DINE_IN', 'TAKEAWAY')),
    order_time TEXT DEFAULT CURRENT_TIMESTAMP,
    total_amount REAL NOT NULL,
    customer_phone TEXT,
    notes TEXT,
    -- 在店消费字段
    table_number TEXT,
    has_private_room INTEGER DEFAULT 0,
    private_room_fee REAL DEFAULT 0.0,
    -- 外卖字段
    delivery_time TEXT,
    delivery_address TEXT,
    delivery_fee REAL DEFAULT 0.0,
    created_time TEXT DEFAULT CURRENT_TIMESTAMP
);

-- 订单项表
CREATE TABLE IF NOT EXISTS order_item (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    order_id TEXT NOT NULL,
    dish_id TEXT NOT NULL,
    dish_name TEXT NOT NULL,
    unit_price REAL NOT NULL,
    quantity INTEGER NOT NULL,
    total_price REAL NOT NULL,
    notes TEXT,
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dish(id)
);

-- 插入默认管理员
INSERT OR IGNORE INTO admin (username, password, name, email) VALUES 
('admin', '123456', '系统管理员', '<EMAIL>');

-- 插入默认菜品数据
INSERT OR IGNORE INTO dish (id, name, price, category, description) VALUES 
-- 主食类
('001', '宫保鸡丁', 28.00, '主食', '经典川菜，鸡丁配花生米'),
('002', '红烧肉', 35.00, '主食', '传统红烧肉，肥瘦相间'),
('003', '麻婆豆腐', 18.00, '主食', '四川名菜，麻辣鲜香'),
('004', '糖醋里脊', 32.00, '主食', '酸甜可口的经典菜品'),
-- 汤类
('101', '西红柿鸡蛋汤', 12.00, '汤类', '清淡营养的家常汤'),
('102', '冬瓜排骨汤', 25.00, '汤类', '清热解腻，营养丰富'),
('103', '紫菜蛋花汤', 10.00, '汤类', '简单清爽的汤品'),
-- 素食类
('201', '清炒时蔬', 15.00, '素食', '当季新鲜蔬菜'),
('202', '干煸四季豆', 20.00, '素食', '川菜经典素食'),
('203', '蒜蓉菠菜', 12.00, '素食', '营养丰富的绿叶菜'),
-- 饮品类
('301', '鲜榨橙汁', 15.00, '饮品', '新鲜橙子现榨'),
('302', '柠檬蜂蜜茶', 12.00, '饮品', '清香解腻'),
('303', '绿豆汤', 8.00, '饮品', '清热降火');

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_orders_order_time ON orders(order_time);
CREATE INDEX IF NOT EXISTS idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX IF NOT EXISTS idx_order_item_order_id ON order_item(order_id);
CREATE INDEX IF NOT EXISTS idx_dish_category ON dish(category);
CREATE INDEX IF NOT EXISTS idx_dish_available ON dish(available);
