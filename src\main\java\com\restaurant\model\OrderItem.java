package com.restaurant.model;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 订单项实体类
 */
public class OrderItem {
    private Dish dish;              // 菜品
    private int quantity;           // 数量
    private BigDecimal unitPrice;   // 单价（下单时的价格，包含折扣）
    private String notes;           // 备注
    
    /**
     * 默认构造函数
     */
    public OrderItem() {
    }
    
    /**
     * 带参数的构造函数
     */
    public OrderItem(Dish dish, int quantity) {
        this.dish = dish;
        this.quantity = quantity;
        this.unitPrice = dish.getDiscountedPrice();
    }
    
    /**
     * 完整构造函数
     */
    public OrderItem(Dish dish, int quantity, BigDecimal unitPrice, String notes) {
        this.dish = dish;
        this.quantity = quantity;
        this.unitPrice = unitPrice;
        this.notes = notes;
    }
    
    /**
     * 计算该订单项的总价
     * @return 总价
     */
    public BigDecimal getTotalPrice() {
        return unitPrice.multiply(BigDecimal.valueOf(quantity));
    }
    
    // Getter和Setter方法
    public Dish getDish() {
        return dish;
    }
    
    public void setDish(Dish dish) {
        this.dish = dish;
    }
    
    public int getQuantity() {
        return quantity;
    }
    
    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }
    
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }
    
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        OrderItem orderItem = (OrderItem) o;
        return quantity == orderItem.quantity &&
                Objects.equals(dish, orderItem.dish) &&
                Objects.equals(unitPrice, orderItem.unitPrice);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(dish, quantity, unitPrice);
    }
    
    @Override
    public String toString() {
        return String.format("OrderItem{dish=%s, quantity=%d, unitPrice=%s, totalPrice=%s}",
                dish.getName(), quantity, unitPrice, getTotalPrice());
    }
}
