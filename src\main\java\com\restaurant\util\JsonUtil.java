package com.restaurant.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import java.io.IOException;

/**
 * JSON工具类
 * JSON Utility Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class JsonUtil {
    
    private static final ObjectMapper objectMapper;
    
    static {
        objectMapper = new ObjectMapper();
        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());
        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        // 启用缩进输出
        objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
    }
    
    /**
     * 对象转JSON字符串
     * @param object 对象
     * @return JSON字符串
     * @throws IOException IO异常
     */
    public static String toJson(Object object) throws IOException {
        return objectMapper.writeValueAsString(object);
    }
    
    /**
     * JSON字符串转对象
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象
     * @throws IOException IO异常
     */
    public static <T> T fromJson(String json, Class<T> clazz) throws IOException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return objectMapper.readValue(json, clazz);
    }
    
    /**
     * JSON字符串转对象（支持泛型）
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 对象
     * @throws IOException IO异常
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) throws IOException {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        return objectMapper.readValue(json, typeReference);
    }
    
    /**
     * 对象转JSON并写入文件
     * @param object 对象
     * @param filePath 文件路径
     * @throws IOException IO异常
     */
    public static void writeToFile(Object object, String filePath) throws IOException {
        String json = toJson(object);
        FileUtil.writeFile(filePath, json);
    }
    
    /**
     * 从文件读取JSON并转换为对象
     * @param filePath 文件路径
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象
     * @throws IOException IO异常
     */
    public static <T> T readFromFile(String filePath, Class<T> clazz) throws IOException {
        String json = FileUtil.readFile(filePath);
        return fromJson(json, clazz);
    }
    
    /**
     * 从文件读取JSON并转换为对象（支持泛型）
     * @param filePath 文件路径
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 对象
     * @throws IOException IO异常
     */
    public static <T> T readFromFile(String filePath, TypeReference<T> typeReference) throws IOException {
        String json = FileUtil.readFile(filePath);
        return fromJson(json, typeReference);
    }
    
    /**
     * 获取ObjectMapper实例
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }
}
