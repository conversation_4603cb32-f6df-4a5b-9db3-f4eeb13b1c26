-- 餐厅自助点餐系统数据库初始化脚本 (MySQL)
-- Restaurant Self-Ordering System Database Initialization Script (MySQL)

-- 创建数据库
CREATE DATABASE IF NOT EXISTS restaurant_db DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE restaurant_db;

-- 管理员表
CREATE TABLE IF NOT EXISTS admin (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    email VARCHAR(100) COMMENT '邮箱',
    active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    last_login_time DATETIME COMMENT '最后登录时间',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '管理员表';

-- 菜品表
CREATE TABLE IF NOT EXISTS dish (
    id VARCHAR(20) PRIMARY KEY COMMENT '菜品ID',
    name VARCHAR(100) NOT NULL COMMENT '菜品名称',
    price DECIMAL(10,2) NOT NULL COMMENT '菜品价格',
    category VARCHAR(50) NOT NULL COMMENT '菜品分类',
    description TEXT COMMENT '菜品描述',
    available BOOLEAN DEFAULT TRUE COMMENT '是否可用',
    discount DECIMAL(3,2) DEFAULT 1.00 COMMENT '折扣(0.1-1.0)',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '菜品表';

-- 订单表
CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(20) PRIMARY KEY COMMENT '订单ID',
    order_type ENUM('DINE_IN', 'TAKEAWAY') NOT NULL COMMENT '订单类型',
    order_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    customer_phone VARCHAR(20) COMMENT '客户手机号',
    notes TEXT COMMENT '备注',
    -- 在店消费字段
    table_number VARCHAR(20) COMMENT '餐桌号',
    has_private_room BOOLEAN DEFAULT FALSE COMMENT '是否有包厢',
    private_room_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '包厢费',
    -- 外卖字段
    delivery_time DATETIME COMMENT '送餐时间',
    delivery_address TEXT COMMENT '送餐地址',
    delivery_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '外卖服务费',
    created_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) COMMENT '订单表';

-- 订单项表
CREATE TABLE IF NOT EXISTS order_item (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id VARCHAR(20) NOT NULL COMMENT '订单ID',
    dish_id VARCHAR(20) NOT NULL COMMENT '菜品ID',
    dish_name VARCHAR(100) NOT NULL COMMENT '菜品名称',
    unit_price DECIMAL(10,2) NOT NULL COMMENT '单价',
    quantity INT NOT NULL COMMENT '数量',
    total_price DECIMAL(10,2) NOT NULL COMMENT '小计',
    notes TEXT COMMENT '备注',
    FOREIGN KEY (order_id) REFERENCES orders(id) ON DELETE CASCADE,
    FOREIGN KEY (dish_id) REFERENCES dish(id)
) COMMENT '订单项表';

-- 插入默认管理员
INSERT INTO admin (username, password, name, email) VALUES 
('admin', '123456', '系统管理员', '<EMAIL>')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入默认菜品数据
INSERT INTO dish (id, name, price, category, description) VALUES 
-- 主食类
('001', '宫保鸡丁', 28.00, '主食', '经典川菜，鸡丁配花生米'),
('002', '红烧肉', 35.00, '主食', '传统红烧肉，肥瘦相间'),
('003', '麻婆豆腐', 18.00, '主食', '四川名菜，麻辣鲜香'),
('004', '糖醋里脊', 32.00, '主食', '酸甜可口的经典菜品'),
-- 汤类
('101', '西红柿鸡蛋汤', 12.00, '汤类', '清淡营养的家常汤'),
('102', '冬瓜排骨汤', 25.00, '汤类', '清热解腻，营养丰富'),
('103', '紫菜蛋花汤', 10.00, '汤类', '简单清爽的汤品'),
-- 素食类
('201', '清炒时蔬', 15.00, '素食', '当季新鲜蔬菜'),
('202', '干煸四季豆', 20.00, '素食', '川菜经典素食'),
('203', '蒜蓉菠菜', 12.00, '素食', '营养丰富的绿叶菜'),
-- 饮品类
('301', '鲜榨橙汁', 15.00, '饮品', '新鲜橙子现榨'),
('302', '柠檬蜂蜜茶', 12.00, '饮品', '清香解腻'),
('303', '绿豆汤', 8.00, '饮品', '清热降火')
ON DUPLICATE KEY UPDATE 
    name = VALUES(name),
    price = VALUES(price),
    category = VALUES(category),
    description = VALUES(description);

-- 创建索引
CREATE INDEX idx_orders_order_time ON orders(order_time);
CREATE INDEX idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX idx_order_item_order_id ON order_item(order_id);
CREATE INDEX idx_dish_category ON dish(category);
CREATE INDEX idx_dish_available ON dish(available);
