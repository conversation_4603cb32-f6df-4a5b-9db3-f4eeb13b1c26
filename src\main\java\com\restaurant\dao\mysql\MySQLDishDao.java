package com.restaurant.dao.mysql;

import com.restaurant.model.Dish;
import com.restaurant.util.DatabaseConfig;

import java.math.BigDecimal;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL菜品数据访问对象
 */
public class MySQLDishDao {
    
    private final DatabaseConfig dbConfig;
    
    public MySQLDishDao() {
        this.dbConfig = DatabaseConfig.getInstance();
    }
    
    /**
     * 获取所有菜品
     */
    public List<Dish> getAllDishes() {
        List<Dish> dishes = new ArrayList<>();
        String sql = "SELECT * FROM dish ORDER BY category, id";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                dishes.add(mapResultSetToDish(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取所有菜品失败: " + e.getMessage());
        }
        
        return dishes;
    }
    
    /**
     * 根据ID查找菜品
     */
    public Optional<Dish> findById(String id) {
        String sql = "SELECT * FROM dish WHERE id = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, id);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToDish(rs));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据ID查找菜品失败: " + e.getMessage());
        }
        
        return Optional.empty();
    }
    
    /**
     * 根据名称查找菜品
     */
    public List<Dish> findByName(String name) {
        List<Dish> dishes = new ArrayList<>();
        String sql = "SELECT * FROM dish WHERE name LIKE ? ORDER BY name";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, "%" + name + "%");
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    dishes.add(mapResultSetToDish(rs));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据名称查找菜品失败: " + e.getMessage());
        }
        
        return dishes;
    }
    
    /**
     * 根据分类查找菜品
     */
    public List<Dish> findByCategory(String category) {
        List<Dish> dishes = new ArrayList<>();
        String sql = "SELECT * FROM dish WHERE category = ? ORDER BY id";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, category);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    dishes.add(mapResultSetToDish(rs));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据分类查找菜品失败: " + e.getMessage());
        }
        
        return dishes;
    }
    
    /**
     * 获取所有可用菜品
     */
    public List<Dish> getAvailableDishes() {
        List<Dish> dishes = new ArrayList<>();
        String sql = "SELECT * FROM dish WHERE available = 1 ORDER BY category, id";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                dishes.add(mapResultSetToDish(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取可用菜品失败: " + e.getMessage());
        }
        
        return dishes;
    }
    
    /**
     * 添加菜品
     */
    public boolean addDish(Dish dish) {
        String sql = "INSERT INTO dish (id, name, price, category, description, available, discount) VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, dish.getId());
            stmt.setString(2, dish.getName());
            stmt.setBigDecimal(3, dish.getPrice());
            stmt.setString(4, dish.getCategory());
            stmt.setString(5, dish.getDescription());
            stmt.setBoolean(6, dish.isAvailable());
            stmt.setBigDecimal(7, dish.getDiscount());
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("添加菜品失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新菜品
     */
    public boolean updateDish(Dish dish) {
        String sql = "UPDATE dish SET name = ?, price = ?, category = ?, description = ?, available = ?, discount = ?, updated_time = CURRENT_TIMESTAMP WHERE id = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, dish.getName());
            stmt.setBigDecimal(2, dish.getPrice());
            stmt.setString(3, dish.getCategory());
            stmt.setString(4, dish.getDescription());
            stmt.setBoolean(5, dish.isAvailable());
            stmt.setBigDecimal(6, dish.getDiscount());
            stmt.setString(7, dish.getId());
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("更新菜品失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 删除菜品
     */
    public boolean deleteDish(String id) {
        String sql = "DELETE FROM dish WHERE id = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, id);
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("删除菜品失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有分类
     */
    public List<String> getAllCategories() {
        List<String> categories = new ArrayList<>();
        String sql = "SELECT DISTINCT category FROM dish ORDER BY category";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                categories.add(rs.getString("category"));
            }
            
        } catch (SQLException e) {
            System.err.println("获取分类失败: " + e.getMessage());
        }
        
        return categories;
    }
    
    /**
     * 将ResultSet映射为Dish对象
     */
    private Dish mapResultSetToDish(ResultSet rs) throws SQLException {
        return new Dish(
            rs.getString("id"),
            rs.getString("name"),
            rs.getBigDecimal("price"),
            rs.getString("category"),
            rs.getString("description"),
            rs.getBoolean("available"),
            rs.getBigDecimal("discount")
        );
    }
}
