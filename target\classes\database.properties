# 数据库配置文件
# Database Configuration

# 数据存储类型 (json/mysql/sqlite)
storage.type=json

# JSON文件存储配置
json.data.directory=data
json.encoding=UTF-8
json.backup.enabled=true
json.backup.directory=data/backup

# MySQL数据库配置 (备用)
mysql.host=localhost
mysql.port=3306
mysql.database=restaurant_db
mysql.username=root
mysql.password=
mysql.driver=com.mysql.cj.jdbc.Driver
mysql.url=jdbc:mysql://${mysql.host}:${mysql.port}/${mysql.database}?useSSL=false&serverTimezone=UTC&characterEncoding=utf8

# SQLite数据库配置 (备用)
sqlite.file=data/restaurant.db
sqlite.driver=org.sqlite.JDBC
sqlite.url=jdbc:sqlite:${sqlite.file}

# 连接池配置
connection.pool.initial.size=5
connection.pool.max.size=20
connection.pool.timeout=30000

# 数据初始化配置
data.init.enabled=true
data.init.create.default.admin=true
data.init.create.default.dishes=true
