package com.restaurant.service;

import com.restaurant.dao.AdminDao;
import com.restaurant.model.Admin;

import java.util.Optional;

/**
 * 管理员服务
 * Admin Service
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class AdminService {
    
    private final AdminDao adminDao;
    private Admin currentAdmin; // 当前登录的管理员
    
    public AdminService() {
        this.adminDao = new AdminDao();
    }
    
    /**
     * 管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 是否登录成功
     */
    public boolean login(String username, String password) {
        if (username == null || username.trim().isEmpty() || 
            password == null || password.trim().isEmpty()) {
            return false;
        }
        
        Optional<Admin> adminOpt = adminDao.validateLogin(username.trim(), password);
        if (adminOpt.isPresent()) {
            currentAdmin = adminOpt.get();
            // 更新最后登录时间
            adminDao.updateLastLoginTime(username.trim());
            return true;
        }
        
        return false;
    }
    
    /**
     * 管理员登出
     */
    public void logout() {
        currentAdmin = null;
    }
    
    /**
     * 检查是否已登录
     * @return 是否已登录
     */
    public boolean isLoggedIn() {
        return currentAdmin != null;
    }
    
    /**
     * 获取当前登录的管理员
     * @return 当前管理员
     */
    public Admin getCurrentAdmin() {
        return currentAdmin;
    }
    
    /**
     * 修改密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    public boolean changePassword(String oldPassword, String newPassword) {
        if (!isLoggedIn() || oldPassword == null || newPassword == null) {
            return false;
        }
        
        if (!currentAdmin.validatePassword(oldPassword)) {
            return false;
        }
        
        if (newPassword.trim().length() < 6) {
            return false; // 密码长度至少6位
        }
        
        currentAdmin.setPassword(newPassword);
        return adminDao.updateAdmin(currentAdmin);
    }
    
    /**
     * 更新管理员信息
     * @param name 姓名
     * @param email 邮箱
     * @return 是否更新成功
     */
    public boolean updateProfile(String name, String email) {
        if (!isLoggedIn()) {
            return false;
        }
        
        if (name != null && !name.trim().isEmpty()) {
            currentAdmin.setName(name.trim());
        }
        
        if (email != null && !email.trim().isEmpty()) {
            currentAdmin.setEmail(email.trim());
        }
        
        return adminDao.updateAdmin(currentAdmin);
    }
    
    /**
     * 验证用户名格式
     * @param username 用户名
     * @return 验证结果
     */
    public String validateUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            return "用户名不能为空";
        }
        
        username = username.trim();
        if (username.length() < 3 || username.length() > 20) {
            return "用户名长度必须在3-20个字符之间";
        }
        
        if (!username.matches("^[a-zA-Z0-9_]+$")) {
            return "用户名只能包含字母、数字和下划线";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 验证密码格式
     * @param password 密码
     * @return 验证结果
     */
    public String validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return "密码不能为空";
        }
        
        if (password.length() < 6 || password.length() > 20) {
            return "密码长度必须在6-20个字符之间";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 验证邮箱格式
     * @param email 邮箱
     * @return 验证结果
     */
    public String validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return null; // 邮箱可以为空
        }
        
        email = email.trim();
        if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            return "邮箱格式不正确";
        }
        
        return null; // 验证通过
    }
}
