package com.restaurant.ui;

import com.restaurant.util.SystemConfig;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;

/**
 * 系统设置对话框
 */
public class SystemSettingsDialog extends JDialog {
    
    // UI组件
    private JTextField deliveryFeeField;
    private JTextField privateRoomFeeField;
    private JSpinner maxOrderItemsSpinner;
    private JSpinner maxQuantitySpinner;
    private JTextField minDiscountField;
    private JTextField maxDiscountField;
    
    public SystemSettingsDialog(Frame parent) {
        super(parent, "系统设置", true);
        
        initializeComponents();
        loadCurrentSettings();
        
        setSize(400, 300);
        setLocationRelativeTo(parent);
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // 创建设置面板
        JPanel settingsPanel = createSettingsPanel();
        add(settingsPanel, BorderLayout.CENTER);
        
        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建设置面板
     */
    private JPanel createSettingsPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 默认外卖费
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("默认外卖费:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        deliveryFeeField = new JTextField(10);
        panel.add(deliveryFeeField, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("元"), gbc);
        
        // 默认包厢费
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("默认包厢费:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        privateRoomFeeField = new JTextField(10);
        panel.add(privateRoomFeeField, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("元"), gbc);
        
        // 最大订单项数
        gbc.gridx = 0; gbc.gridy = 2;
        panel.add(new JLabel("最大订单项数:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        maxOrderItemsSpinner = new JSpinner(new SpinnerNumberModel(50, 1, 100, 1));
        panel.add(maxOrderItemsSpinner, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("项"), gbc);
        
        // 单项最大数量
        gbc.gridx = 0; gbc.gridy = 3;
        panel.add(new JLabel("单项最大数量:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        maxQuantitySpinner = new JSpinner(new SpinnerNumberModel(99, 1, 999, 1));
        panel.add(maxQuantitySpinner, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("份"), gbc);
        
        // 最小折扣
        gbc.gridx = 0; gbc.gridy = 4;
        panel.add(new JLabel("最小折扣:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        minDiscountField = new JTextField(10);
        panel.add(minDiscountField, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("(0.1-1.0)"), gbc);
        
        // 最大折扣
        gbc.gridx = 0; gbc.gridy = 5;
        panel.add(new JLabel("最大折扣:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        maxDiscountField = new JTextField(10);
        panel.add(maxDiscountField, gbc);
        gbc.gridx = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("(0.1-1.0)"), gbc);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        JButton saveButton = new JButton("保存设置");
        saveButton.addActionListener(new SaveActionListener());
        
        JButton resetButton = new JButton("恢复默认");
        resetButton.addActionListener(new ResetActionListener());
        
        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(saveButton);
        panel.add(resetButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    /**
     * 加载当前设置
     */
    private void loadCurrentSettings() {
        deliveryFeeField.setText(SystemConfig.DEFAULT_DELIVERY_FEE.toString());
        privateRoomFeeField.setText(SystemConfig.DEFAULT_PRIVATE_ROOM_FEE.toString());
        maxOrderItemsSpinner.setValue(SystemConfig.MAX_ORDER_ITEMS);
        maxQuantitySpinner.setValue(SystemConfig.MAX_QUANTITY_PER_ITEM);
        minDiscountField.setText(SystemConfig.MIN_DISCOUNT.toString());
        maxDiscountField.setText(SystemConfig.MAX_DISCOUNT.toString());
    }
    
    /**
     * 验证输入
     */
    private String validateInput() {
        try {
            BigDecimal deliveryFee = new BigDecimal(deliveryFeeField.getText().trim());
            if (deliveryFee.compareTo(BigDecimal.ZERO) < 0) {
                return "外卖费不能为负数";
            }
        } catch (NumberFormatException e) {
            return "外卖费格式不正确";
        }
        
        try {
            BigDecimal privateRoomFee = new BigDecimal(privateRoomFeeField.getText().trim());
            if (privateRoomFee.compareTo(BigDecimal.ZERO) < 0) {
                return "包厢费不能为负数";
            }
        } catch (NumberFormatException e) {
            return "包厢费格式不正确";
        }
        
        try {
            BigDecimal minDiscount = new BigDecimal(minDiscountField.getText().trim());
            BigDecimal maxDiscount = new BigDecimal(maxDiscountField.getText().trim());
            
            if (minDiscount.compareTo(new BigDecimal("0.1")) < 0 || 
                minDiscount.compareTo(BigDecimal.ONE) > 0) {
                return "最小折扣必须在0.1-1.0之间";
            }
            
            if (maxDiscount.compareTo(new BigDecimal("0.1")) < 0 || 
                maxDiscount.compareTo(BigDecimal.ONE) > 0) {
                return "最大折扣必须在0.1-1.0之间";
            }
            
            if (minDiscount.compareTo(maxDiscount) > 0) {
                return "最小折扣不能大于最大折扣";
            }
            
        } catch (NumberFormatException e) {
            return "折扣格式不正确";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 保存按钮事件监听器
     */
    private class SaveActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String validationError = validateInput();
            if (validationError != null) {
                JOptionPane.showMessageDialog(SystemSettingsDialog.this, 
                    validationError, "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 这里可以保存设置到配置文件或数据库
            // 目前只是显示成功消息
            JOptionPane.showMessageDialog(SystemSettingsDialog.this, 
                "设置保存成功\n注意：部分设置需要重启系统后生效", 
                "保存成功", JOptionPane.INFORMATION_MESSAGE);
            
            dispose();
        }
    }
    
    /**
     * 重置按钮事件监听器
     */
    private class ResetActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            int result = JOptionPane.showConfirmDialog(SystemSettingsDialog.this, 
                "确定要恢复默认设置吗？", "确认重置", 
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            
            if (result == JOptionPane.YES_OPTION) {
                loadCurrentSettings();
                JOptionPane.showMessageDialog(SystemSettingsDialog.this, 
                    "已恢复默认设置", "重置成功", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }
}
