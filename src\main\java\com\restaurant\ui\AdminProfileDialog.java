package com.restaurant.ui;

import com.restaurant.model.Admin;
import com.restaurant.service.AdminService;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 管理员个人信息对话框
 * Admin Profile Dialog
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class AdminProfileDialog extends JDialog {
    
    private final AdminService adminService;
    private final Admin admin;
    
    // UI组件
    private JTextField usernameField;
    private JTextField nameField;
    private JTextField emailField;
    private JPasswordField oldPasswordField;
    private JPasswordField newPasswordField;
    private JPasswordField confirmPasswordField;
    
    public AdminProfileDialog(Frame parent, AdminService adminService) {
        super(parent, "个人信息管理", true);
        this.adminService = adminService;
        this.admin = adminService.getCurrentAdmin();
        
        initializeComponents();
        loadAdminData();
        
        setSize(400, 350);
        setLocationRelativeTo(parent);
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // 创建表单面板
        JPanel formPanel = createFormPanel();
        add(formPanel, BorderLayout.CENTER);
        
        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建表单面板
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 用户名（只读）
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("用户名:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        usernameField = new JTextField(20);
        usernameField.setEditable(false);
        usernameField.setBackground(Color.LIGHT_GRAY);
        panel.add(usernameField, gbc);
        
        // 姓名
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("姓名:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        nameField = new JTextField(20);
        panel.add(nameField, gbc);
        
        // 邮箱
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("邮箱:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        emailField = new JTextField(20);
        panel.add(emailField, gbc);
        
        // 分隔线
        gbc.gridx = 0; gbc.gridy = 3; gbc.gridwidth = 2; gbc.fill = GridBagConstraints.HORIZONTAL;
        JSeparator separator = new JSeparator();
        separator.setBorder(BorderFactory.createTitledBorder("修改密码"));
        panel.add(separator, gbc);
        
        // 原密码
        gbc.gridx = 0; gbc.gridy = 4; gbc.gridwidth = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("原密码:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        oldPasswordField = new JPasswordField(20);
        panel.add(oldPasswordField, gbc);
        
        // 新密码
        gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("新密码:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        newPasswordField = new JPasswordField(20);
        panel.add(newPasswordField, gbc);
        
        // 确认密码
        gbc.gridx = 0; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("确认密码:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        confirmPasswordField = new JPasswordField(20);
        panel.add(confirmPasswordField, gbc);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        JButton updateInfoButton = new JButton("更新信息");
        updateInfoButton.addActionListener(new UpdateInfoActionListener());
        
        JButton changePasswordButton = new JButton("修改密码");
        changePasswordButton.addActionListener(new ChangePasswordActionListener());
        
        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(updateInfoButton);
        panel.add(changePasswordButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    /**
     * 加载管理员数据
     */
    private void loadAdminData() {
        if (admin != null) {
            usernameField.setText(admin.getUsername());
            nameField.setText(admin.getName());
            emailField.setText(admin.getEmail() != null ? admin.getEmail() : "");
        }
    }
    
    /**
     * 更新信息按钮事件监听器
     */
    private class UpdateInfoActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String name = nameField.getText().trim();
            String email = emailField.getText().trim();
            
            if (name.isEmpty()) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "姓名不能为空", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 验证邮箱格式
            if (!email.isEmpty()) {
                String emailValidation = adminService.validateEmail(email);
                if (emailValidation != null) {
                    JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                        emailValidation, "输入错误", JOptionPane.ERROR_MESSAGE);
                    return;
                }
            }
            
            // 更新信息
            if (adminService.updateProfile(name, email)) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "个人信息更新成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                loadAdminData(); // 重新加载数据
            } else {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "个人信息更新失败", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 修改密码按钮事件监听器
     */
    private class ChangePasswordActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            String oldPassword = new String(oldPasswordField.getPassword());
            String newPassword = new String(newPasswordField.getPassword());
            String confirmPassword = new String(confirmPasswordField.getPassword());
            
            // 验证输入
            if (oldPassword.isEmpty()) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "请输入原密码", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            if (newPassword.isEmpty()) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "请输入新密码", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            if (!newPassword.equals(confirmPassword)) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "两次输入的密码不一致", "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 验证密码格式
            String passwordValidation = adminService.validatePassword(newPassword);
            if (passwordValidation != null) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    passwordValidation, "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 修改密码
            if (adminService.changePassword(oldPassword, newPassword)) {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "密码修改成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                
                // 清空密码字段
                oldPasswordField.setText("");
                newPasswordField.setText("");
                confirmPasswordField.setText("");
            } else {
                JOptionPane.showMessageDialog(AdminProfileDialog.this, 
                    "密码修改失败，请检查原密码是否正确", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
}
