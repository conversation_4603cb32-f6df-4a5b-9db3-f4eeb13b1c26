package com.restaurant.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 订单抽象基类
 * Abstract Order Base Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public abstract class Order {
    protected String orderId;                    // 订单编号
    protected LocalDateTime orderTime;           // 下单时间
    protected List<OrderItem> orderItems;       // 订单项列表
    protected OrderType orderType;              // 订单类型
    protected BigDecimal totalAmount;           // 总金额
    protected String customerPhone;             // 客户手机号（可选）
    protected String notes;                     // 备注
    
    /**
     * 默认构造函数
     */
    public Order() {
        this.orderItems = new ArrayList<>();
        this.orderTime = LocalDateTime.now();
    }
    
    /**
     * 带参数的构造函数
     */
    public Order(String orderId, OrderType orderType) {
        this();
        this.orderId = orderId;
        this.orderType = orderType;
    }
    
    /**
     * 添加订单项
     * @param orderItem 订单项
     */
    public void addOrderItem(OrderItem orderItem) {
        if (orderItem != null) {
            orderItems.add(orderItem);
            calculateTotalAmount();
        }
    }
    
    /**
     * 移除订单项
     * @param orderItem 订单项
     */
    public void removeOrderItem(OrderItem orderItem) {
        if (orderItems.remove(orderItem)) {
            calculateTotalAmount();
        }
    }
    
    /**
     * 计算总金额（抽象方法，由子类实现具体计算逻辑）
     */
    public abstract void calculateTotalAmount();
    
    /**
     * 获取订单详细信息（抽象方法，由子类实现）
     */
    public abstract String getOrderDetails();
    
    /**
     * 计算订单项的小计
     * @return 订单项小计
     */
    protected BigDecimal calculateItemsSubtotal() {
        return orderItems.stream()
                .map(OrderItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    // Getter和Setter方法
    public String getOrderId() {
        return orderId;
    }
    
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    
    public LocalDateTime getOrderTime() {
        return orderTime;
    }
    
    public void setOrderTime(LocalDateTime orderTime) {
        this.orderTime = orderTime;
    }
    
    public List<OrderItem> getOrderItems() {
        return new ArrayList<>(orderItems);
    }
    
    public void setOrderItems(List<OrderItem> orderItems) {
        this.orderItems = orderItems != null ? new ArrayList<>(orderItems) : new ArrayList<>();
        calculateTotalAmount();
    }
    
    public OrderType getOrderType() {
        return orderType;
    }
    
    public void setOrderType(OrderType orderType) {
        this.orderType = orderType;
    }
    
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }
    
    protected void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }
    
    public String getCustomerPhone() {
        return customerPhone;
    }
    
    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }
    
    public String getNotes() {
        return notes;
    }
    
    public void setNotes(String notes) {
        this.notes = notes;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Order order = (Order) o;
        return Objects.equals(orderId, order.orderId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(orderId);
    }
    
    @Override
    public String toString() {
        return String.format("Order{orderId='%s', orderType=%s, totalAmount=%s, orderTime=%s}",
                orderId, orderType, totalAmount, orderTime);
    }
}
