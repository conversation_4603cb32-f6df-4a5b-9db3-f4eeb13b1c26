package com.restaurant.service;

import com.restaurant.model.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 销售统计服务
 * Sales Statistics Service
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class SalesStatisticsService {
    
    private final OrderService orderService;
    
    public SalesStatisticsService() {
        this.orderService = new OrderService();
    }
    
    /**
     * 销售统计数据类
     */
    public static class SalesStatistics {
        private LocalDate date;                          // 统计日期
        private int totalOrders;                         // 总订单数
        private BigDecimal totalSales;                   // 总销售额
        private int dineInOrders;                        // 在店消费订单数
        private BigDecimal dineInSales;                  // 在店消费销售额
        private int takeawayOrders;                      // 外卖订单数
        private BigDecimal takeawaySales;                // 外卖销售额
        private Map<String, DishSalesInfo> dishSales;    // 各菜品销售情况
        
        public SalesStatistics(LocalDate date) {
            this.date = date;
            this.totalSales = BigDecimal.ZERO;
            this.dineInSales = BigDecimal.ZERO;
            this.takeawaySales = BigDecimal.ZERO;
            this.dishSales = new HashMap<>();
        }
        
        // Getter方法
        public LocalDate getDate() { return date; }
        public int getTotalOrders() { return totalOrders; }
        public BigDecimal getTotalSales() { return totalSales; }
        public int getDineInOrders() { return dineInOrders; }
        public BigDecimal getDineInSales() { return dineInSales; }
        public int getTakeawayOrders() { return takeawayOrders; }
        public BigDecimal getTakeawaySales() { return takeawaySales; }
        public Map<String, DishSalesInfo> getDishSales() { return dishSales; }
        
        // Setter方法
        public void setTotalOrders(int totalOrders) { this.totalOrders = totalOrders; }
        public void setTotalSales(BigDecimal totalSales) { this.totalSales = totalSales; }
        public void setDineInOrders(int dineInOrders) { this.dineInOrders = dineInOrders; }
        public void setDineInSales(BigDecimal dineInSales) { this.dineInSales = dineInSales; }
        public void setTakeawayOrders(int takeawayOrders) { this.takeawayOrders = takeawayOrders; }
        public void setTakeawaySales(BigDecimal takeawaySales) { this.takeawaySales = takeawaySales; }
        
        /**
         * 获取在店消费占比
         * @return 在店消费占比
         */
        public double getDineInPercentage() {
            if (totalOrders == 0) return 0.0;
            return (double) dineInOrders / totalOrders * 100;
        }
        
        /**
         * 获取外卖占比
         * @return 外卖占比
         */
        public double getTakeawayPercentage() {
            if (totalOrders == 0) return 0.0;
            return (double) takeawayOrders / totalOrders * 100;
        }
    }
    
    /**
     * 菜品销售信息类
     */
    public static class DishSalesInfo {
        private String dishId;
        private String dishName;
        private int quantity;
        private BigDecimal totalAmount;
        
        public DishSalesInfo(String dishId, String dishName) {
            this.dishId = dishId;
            this.dishName = dishName;
            this.quantity = 0;
            this.totalAmount = BigDecimal.ZERO;
        }
        
        public void addSale(int qty, BigDecimal amount) {
            this.quantity += qty;
            this.totalAmount = this.totalAmount.add(amount);
        }
        
        // Getter方法
        public String getDishId() { return dishId; }
        public String getDishName() { return dishName; }
        public int getQuantity() { return quantity; }
        public BigDecimal getTotalAmount() { return totalAmount; }
    }
    
    /**
     * 获取指定日期的销售统计
     * @param date 日期
     * @return 销售统计
     */
    public SalesStatistics getSalesStatistics(LocalDate date) {
        SalesStatistics statistics = new SalesStatistics(date);
        List<Order> orders = orderService.getOrdersByDate(date);
        
        for (Order order : orders) {
            // 统计订单数和销售额
            statistics.setTotalOrders(statistics.getTotalOrders() + 1);
            statistics.setTotalSales(statistics.getTotalSales().add(order.getTotalAmount()));
            
            // 按订单类型统计
            if (order instanceof DineInOrder) {
                statistics.setDineInOrders(statistics.getDineInOrders() + 1);
                statistics.setDineInSales(statistics.getDineInSales().add(order.getTotalAmount()));
            } else if (order instanceof TakeawayOrder) {
                statistics.setTakeawayOrders(statistics.getTakeawayOrders() + 1);
                statistics.setTakeawaySales(statistics.getTakeawaySales().add(order.getTotalAmount()));
            }
            
            // 统计各菜品销售情况
            for (OrderItem item : order.getOrderItems()) {
                String dishId = item.getDish().getId();
                String dishName = item.getDish().getName();
                
                DishSalesInfo dishInfo = statistics.getDishSales().computeIfAbsent(dishId, 
                    k -> new DishSalesInfo(dishId, dishName));
                dishInfo.addSale(item.getQuantity(), item.getTotalPrice());
            }
        }
        
        return statistics;
    }
    
    /**
     * 生成销售统计报告
     * @param date 日期
     * @return 统计报告
     */
    public String generateSalesReport(LocalDate date) {
        SalesStatistics statistics = getSalesStatistics(date);
        StringBuilder report = new StringBuilder();
        
        report.append("=== ").append(date).append(" 销售统计报告 ===\n\n");
        
        // 总体统计
        report.append("总体统计:\n");
        report.append("  总订单数: ").append(statistics.getTotalOrders()).append("单\n");
        report.append("  总销售额: ").append(statistics.getTotalSales()).append("元\n\n");
        
        // 订单类型统计
        report.append("订单类型统计:\n");
        report.append("  在店消费: ").append(statistics.getDineInOrders()).append("单 (")
              .append(String.format("%.1f", statistics.getDineInPercentage())).append("%)\n");
        report.append("  在店销售额: ").append(statistics.getDineInSales()).append("元\n");
        report.append("  外卖订单: ").append(statistics.getTakeawayOrders()).append("单 (")
              .append(String.format("%.1f", statistics.getTakeawayPercentage())).append("%)\n");
        report.append("  外卖销售额: ").append(statistics.getTakeawaySales()).append("元\n\n");
        
        // 菜品销售统计
        report.append("菜品销售统计:\n");
        if (statistics.getDishSales().isEmpty()) {
            report.append("  暂无菜品销售数据\n");
        } else {
            statistics.getDishSales().values().stream()
                    .sorted((a, b) -> b.getQuantity() - a.getQuantity()) // 按销量降序排列
                    .forEach(dishInfo -> {
                        report.append("  ").append(dishInfo.getDishName())
                              .append(": ").append(dishInfo.getQuantity()).append("份")
                              .append(" (").append(dishInfo.getTotalAmount()).append("元)\n");
                    });
        }
        
        return report.toString();
    }
    
    /**
     * 获取热销菜品（按销量排序）
     * @param date 日期
     * @param limit 返回数量限制
     * @return 热销菜品列表
     */
    public List<DishSalesInfo> getTopSellingDishes(LocalDate date, int limit) {
        SalesStatistics statistics = getSalesStatistics(date);
        return statistics.getDishSales().values().stream()
                .sorted((a, b) -> b.getQuantity() - a.getQuantity())
                .limit(limit)
                .toList();
    }
}
