面向对象程序课程设计任务书

【题目】餐厅自助点餐系统

【目的】
通过设计一个小型的自助点餐系统，训练综合运用所学知识处理实际问题的能力，强化面向对象的程序设计理念，使自己的程序设计与调试水平有一个明显的提高。

【要求】
设计语言采用JAVA，程序设计方法必须采用面向对象的程序设计方法；

【内容简介】 
有一个小型餐厅，现在这个餐厅打算使用自助点餐系统，方便顾客自己点餐，并提供对餐厅销售情况的统计和管理功能。

【设计要求】
设计界面，该系统为两种角色的用户提供服务，一种是餐厅管理员，一种是顾客。餐厅管理员根据账号、密码登录系统。顾客无需登录即可使用系统。
1、顾客通过该餐厅在系统中提供的菜单为自己点餐，菜单以文件形式保存。系统能够根据顾客的要求正确打出订单，订单内容包括订单编号、菜品名称、每个菜品的价格、份数、折扣等；订单分两种，一种是在店消费，在店消费要求包括餐桌号，是否有包厢费，另一种是外卖，外卖要求包括送餐时间，送餐地点，客户手机号，外卖服务费， 
2、系统退出后能保存当天的订单信息，要求每天一个文档，文档名按照日期命名。餐厅管理员可以根据订单编号或手机号查找、删除或修改某个订单，订单按照下单时间先后排序。
3、能够实现对保存在文件中的餐厅菜式和价格的管理，包括，管理员实现对菜品和对应价格的增加、修改、删除、查找，折扣的设置，设置后，顾客在点餐时看到的是新设置后的菜单；
4、系统可根据历史记录对销售情况进行统计，根据餐厅管理员的输入日期统计某天的销售情况并显示（包括一共接了多少单，销售额是多少，各个菜品的销售情况，外卖和在店销售的占比）；
5、除完成上述功能外，也可自主设计功能，有额外加分。
6、用面向对象的程序设计方法设计该系统。本系统涉及的基本对象有订单对象（包括外卖订单和在店消费订单）、订单管理对象、菜单对象、菜品对象、菜品管理对象、系统界面。实现对这些对象的合理抽象和封装，正确定义类之间的关系。界面合理，代码文件组织清晰，命名符合规范，代码注释清楚。



