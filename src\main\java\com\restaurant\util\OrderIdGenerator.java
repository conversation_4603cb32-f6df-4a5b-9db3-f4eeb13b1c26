package com.restaurant.util;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;



/**
 * 订单ID生成器
 */
public class OrderIdGenerator {

    private static final AtomicInteger counter = new AtomicInteger(1);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static String lastDateStr = "";

    /**
     * 生成订单ID
     * 格式：yyyyMMdd + 4位序号
     * 例如：202406240001
     *
     * @return 订单ID
     */
    public static synchronized String generateOrderId() {
        String dateStr = LocalDateTime.now().format(formatter);

        // 如果是新的一天，重置计数器并从数据库获取最大序号
        if (!dateStr.equals(lastDateStr)) {
            lastDateStr = dateStr;
            int maxSequence = getMaxSequenceFromDatabase(dateStr);
            counter.set(maxSequence + 1);
        }

        int sequence = counter.getAndIncrement();
        return String.format("%s%04d", dateStr, sequence);
    }
    
    /**
     * 从数据库获取指定日期的最大序号
     * @param dateStr 日期字符串（yyyyMMdd格式）
     * @return 最大序号，如果没有找到则返回0
     */
    private static int getMaxSequenceFromDatabase(String dateStr) {
        // 由于OrderService使用MySQLOrderDao，这里直接使用MySQL模式
        // 如果MySQL连接失败，则回退到文件模式
        try {
            return getMaxSequenceFromMySQL(dateStr);
        } catch (Exception e) {
            System.err.println("MySQL模式失败，回退到文件模式: " + e.getMessage());
            return getMaxSequenceFromFiles(dateStr);
        }
    }



    /**
     * 从MySQL获取最大序号
     */
    private static int getMaxSequenceFromMySQL(String dateStr) {
        String sql = "SELECT MAX(CAST(SUBSTRING(id, 9) AS UNSIGNED)) as max_seq " +
                    "FROM orders WHERE id LIKE ?";

        try (Connection conn = DatabaseConfig.getInstance().getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, dateStr + "%");

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    int maxSeq = rs.getInt("max_seq");
                    return rs.wasNull() ? 0 : maxSeq;
                }
            }
        } catch (SQLException e) {
            System.err.println("从MySQL获取最大订单序号失败: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * 从文件系统获取最大序号
     */
    private static int getMaxSequenceFromFiles(String dateStr) {
        try {
            java.io.File dataDir = new java.io.File("data");
            if (!dataDir.exists()) {
                return 0;
            }

            java.io.File ordersFile = new java.io.File(dataDir, "orders.json");
            if (!ordersFile.exists()) {
                return 0;
            }

            // 读取订单文件并查找最大序号
            String content = java.nio.file.Files.readString(ordersFile.toPath());
            int maxSeq = 0;

            // 简单的正则匹配查找订单ID
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\"orderId\"\\s*:\\s*\"(" + dateStr + "\\d{4})\"");
            java.util.regex.Matcher matcher = pattern.matcher(content);

            while (matcher.find()) {
                String orderId = matcher.group(1);
                if (orderId.length() >= 12) {
                    try {
                        int seq = Integer.parseInt(orderId.substring(8));
                        maxSeq = Math.max(maxSeq, seq);
                    } catch (NumberFormatException e) {
                        // 忽略格式错误的ID
                    }
                }
            }

            return maxSeq;

        } catch (Exception e) {
            System.err.println("从文件获取最大订单序号失败: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * 重置计数器（通常在新的一天开始时调用）
     */
    public static void resetCounter() {
        counter.set(1);
    }

    /**
     * 获取当前计数器值
     * @return 当前计数器值
     */
    public static int getCurrentCounter() {
        return counter.get();
    }
}
