package com.restaurant.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 订单ID生成器
 * Order ID Generator
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class OrderIdGenerator {
    
    private static final AtomicInteger counter = new AtomicInteger(1);
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    /**
     * 生成订单ID
     * 格式：yyyyMMdd + 4位序号
     * 例如：202406240001
     * 
     * @return 订单ID
     */
    public static String generateOrderId() {
        String dateStr = LocalDateTime.now().format(formatter);
        int sequence = counter.getAndIncrement();
        return String.format("%s%04d", dateStr, sequence);
    }
    
    /**
     * 重置计数器（通常在新的一天开始时调用）
     */
    public static void resetCounter() {
        counter.set(1);
    }
    
    /**
     * 获取当前计数器值
     * @return 当前计数器值
     */
    public static int getCurrentCounter() {
        return counter.get();
    }
}
