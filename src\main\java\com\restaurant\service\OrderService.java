package com.restaurant.service;

import com.restaurant.dao.OrderDao;
import com.restaurant.model.*;
import com.restaurant.util.OrderIdGenerator;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单管理服务
 * Order Management Service
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class OrderService {
    
    private final OrderDao orderDao;
    private final DishService dishService;
    
    public OrderService() {
        this.orderDao = new OrderDao();
        this.dishService = new DishService();
    }
    
    /**
     * 创建在店消费订单
     * @param tableNumber 餐桌号
     * @param hasPrivateRoom 是否有包厢
     * @param privateRoomFee 包厢费
     * @param customerPhone 客户手机号（可选）
     * @return 订单
     */
    public DineInOrder createDineInOrder(String tableNumber, boolean hasPrivateRoom, 
                                        BigDecimal privateRoomFee, String customerPhone) {
        String orderId = OrderIdGenerator.generateOrderId();
        DineInOrder order = new DineInOrder(orderId, tableNumber, hasPrivateRoom, privateRoomFee);
        order.setCustomerPhone(customerPhone);
        return order;
    }
    
    /**
     * 创建外卖订单
     * @param deliveryAddress 送餐地址
     * @param customerPhone 客户手机号
     * @param deliveryTime 送餐时间
     * @param deliveryFee 外卖服务费
     * @return 订单
     */
    public TakeawayOrder createTakeawayOrder(String deliveryAddress, String customerPhone, 
                                           LocalDateTime deliveryTime, BigDecimal deliveryFee) {
        String orderId = OrderIdGenerator.generateOrderId();
        TakeawayOrder order = new TakeawayOrder(orderId, deliveryTime, deliveryAddress, 
                                               customerPhone, deliveryFee);
        return order;
    }
    
    /**
     * 添加订单项到订单
     * @param order 订单
     * @param dishId 菜品ID
     * @param quantity 数量
     * @param notes 备注
     * @return 是否添加成功
     */
    public boolean addOrderItem(Order order, String dishId, int quantity, String notes) {
        if (order == null || dishId == null || quantity <= 0) {
            return false;
        }
        
        Optional<Dish> dishOpt = dishService.findDishById(dishId);
        if (!dishOpt.isPresent() || !dishOpt.get().isAvailable()) {
            return false;
        }
        
        Dish dish = dishOpt.get();
        OrderItem orderItem = new OrderItem(dish, quantity, dish.getDiscountedPrice(), notes);
        order.addOrderItem(orderItem);
        return true;
    }
    
    /**
     * 保存订单
     * @param order 订单
     * @return 是否保存成功
     */
    public boolean saveOrder(Order order) {
        if (order == null || order.getOrderItems().isEmpty()) {
            return false;
        }
        
        // 重新计算总金额
        order.calculateTotalAmount();
        
        return orderDao.saveOrder(order);
    }
    
    /**
     * 获取今日所有订单
     * @return 今日订单列表
     */
    public List<Order> getTodayOrders() {
        return orderDao.getTodayOrders();
    }
    
    /**
     * 获取指定日期的订单
     * @param date 日期
     * @return 订单列表
     */
    public List<Order> getOrdersByDate(LocalDate date) {
        return orderDao.getOrdersByDate(date);
    }
    
    /**
     * 根据订单ID查找订单
     * @param orderId 订单ID
     * @return 订单（可能为空）
     */
    public Optional<Order> findOrderById(String orderId) {
        return orderDao.findById(orderId);
    }
    
    /**
     * 根据手机号查找订单
     * @param phone 手机号
     * @return 订单列表
     */
    public List<Order> findOrdersByPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return List.of();
        }
        return orderDao.findByPhone(phone.trim());
    }
    
    /**
     * 更新订单
     * @param order 订单
     * @return 是否更新成功
     */
    public boolean updateOrder(Order order) {
        if (order == null) {
            return false;
        }
        
        // 重新计算总金额
        order.calculateTotalAmount();
        
        return orderDao.updateOrder(order);
    }
    
    /**
     * 删除订单
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    public boolean deleteOrder(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return false;
        }
        
        return orderDao.deleteOrder(orderId);
    }
    
    /**
     * 获取在店消费订单
     * @param date 日期
     * @return 在店消费订单列表
     */
    public List<DineInOrder> getDineInOrders(LocalDate date) {
        return getOrdersByDate(date).stream()
                .filter(order -> order instanceof DineInOrder)
                .map(order -> (DineInOrder) order)
                .collect(Collectors.toList());
    }
    
    /**
     * 获取外卖订单
     * @param date 日期
     * @return 外卖订单列表
     */
    public List<TakeawayOrder> getTakeawayOrders(LocalDate date) {
        return getOrdersByDate(date).stream()
                .filter(order -> order instanceof TakeawayOrder)
                .map(order -> (TakeawayOrder) order)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证订单数据
     * @param order 订单
     * @return 验证结果
     */
    public String validateOrder(Order order) {
        if (order == null) {
            return "订单不能为空";
        }
        
        if (order.getOrderItems().isEmpty()) {
            return "订单必须包含至少一个菜品";
        }
        
        if (order instanceof DineInOrder) {
            DineInOrder dineInOrder = (DineInOrder) order;
            if (dineInOrder.getTableNumber() == null || dineInOrder.getTableNumber().trim().isEmpty()) {
                return "餐桌号不能为空";
            }
        } else if (order instanceof TakeawayOrder) {
            TakeawayOrder takeawayOrder = (TakeawayOrder) order;
            if (takeawayOrder.getDeliveryAddress() == null || takeawayOrder.getDeliveryAddress().trim().isEmpty()) {
                return "送餐地址不能为空";
            }
            if (takeawayOrder.getCustomerPhone() == null || takeawayOrder.getCustomerPhone().trim().isEmpty()) {
                return "客户手机号不能为空";
            }
        }
        
        return null; // 验证通过
    }
}
