package com.restaurant.model;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

/**
 * 在店消费订单类
 */
public class DineInOrder extends Order {
    private String tableNumber;        // 餐桌号
    private boolean hasPrivateRoom;    // 是否有包厢费
    private BigDecimal privateRoomFee; // 包厢费
    
    /**
     * 默认构造函数
     */
    public DineInOrder() {
        super();
        this.orderType = OrderType.DINE_IN;
        this.hasPrivateRoom = false;
        this.privateRoomFee = BigDecimal.ZERO;
    }
    
    /**
     * 带参数的构造函数
     */
    public DineInOrder(String orderId, String tableNumber) {
        super(orderId, OrderType.DINE_IN);
        this.tableNumber = tableNumber;
        this.hasPrivateRoom = false;
        this.privateRoomFee = BigDecimal.ZERO;
    }
    
    /**
     * 完整构造函数
     */
    public DineInOrder(String orderId, String tableNumber, boolean hasPrivateRoom, BigDecimal privateRoomFee) {
        super(orderId, OrderType.DINE_IN);
        this.tableNumber = tableNumber;
        this.hasPrivateRoom = hasPrivateRoom;
        this.privateRoomFee = privateRoomFee != null ? privateRoomFee : BigDecimal.ZERO;
    }
    
    @Override
    public void calculateTotalAmount() {
        BigDecimal itemsSubtotal = calculateItemsSubtotal();
        BigDecimal roomFee = hasPrivateRoom ? privateRoomFee : BigDecimal.ZERO;
        this.totalAmount = itemsSubtotal.add(roomFee);
    }
    
    @Override
    public String getOrderDetails() {
        StringBuilder details = new StringBuilder();
        details.append("=== 在店消费订单 ===\n");
        details.append("订单编号: ").append(orderId).append("\n");
        details.append("下单时间: ").append(orderTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        details.append("餐桌号: ").append(tableNumber).append("\n");
        
        if (hasPrivateRoom) {
            details.append("包厢费: ").append(privateRoomFee).append("元\n");
        }
        
        if (customerPhone != null && !customerPhone.trim().isEmpty()) {
            details.append("联系电话: ").append(customerPhone).append("\n");
        }
        
        details.append("\n--- 订单明细 ---\n");
        for (OrderItem item : orderItems) {
            details.append(String.format("%s x%d = %.2f元\n", 
                    item.getDish().getName(), 
                    item.getQuantity(), 
                    item.getTotalPrice()));
        }
        
        details.append("\n小计: ").append(calculateItemsSubtotal()).append("元\n");
        if (hasPrivateRoom) {
            details.append("包厢费: ").append(privateRoomFee).append("元\n");
        }
        details.append("总计: ").append(totalAmount).append("元\n");
        
        if (notes != null && !notes.trim().isEmpty()) {
            details.append("备注: ").append(notes).append("\n");
        }
        
        return details.toString();
    }
    
    // Getter和Setter方法
    public String getTableNumber() {
        return tableNumber;
    }
    
    public void setTableNumber(String tableNumber) {
        this.tableNumber = tableNumber;
    }
    
    public boolean isHasPrivateRoom() {
        return hasPrivateRoom;
    }
    
    public void setHasPrivateRoom(boolean hasPrivateRoom) {
        this.hasPrivateRoom = hasPrivateRoom;
        calculateTotalAmount();
    }
    
    public BigDecimal getPrivateRoomFee() {
        return privateRoomFee;
    }
    
    public void setPrivateRoomFee(BigDecimal privateRoomFee) {
        this.privateRoomFee = privateRoomFee != null ? privateRoomFee : BigDecimal.ZERO;
        calculateTotalAmount();
    }
    
    @Override
    public String toString() {
        return String.format("DineInOrder{orderId='%s', tableNumber='%s', hasPrivateRoom=%s, totalAmount=%s}",
                orderId, tableNumber, hasPrivateRoom, totalAmount);
    }
}
