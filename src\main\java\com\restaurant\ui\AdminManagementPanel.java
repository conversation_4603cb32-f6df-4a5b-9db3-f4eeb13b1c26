package com.restaurant.ui;

import com.restaurant.model.*;
import com.restaurant.service.*;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 管理员管理面板
 * Admin Management Panel
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class AdminManagementPanel extends JPanel {
    
    private final MainFrame mainFrame;
    private final DishService dishService;
    private final OrderService orderService;
    private final SalesStatisticsService statisticsService;
    
    private JTabbedPane tabbedPane;
    private JTable dishTable;
    private DefaultTableModel dishTableModel;
    private JTable orderTable;
    private DefaultTableModel orderTableModel;
    private JTextArea statisticsArea;
    
    public AdminManagementPanel(MainFrame mainFrame) {
        this.mainFrame = mainFrame;
        this.dishService = new DishService();
        this.orderService = new OrderService();
        this.statisticsService = new SalesStatisticsService();
        initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        setBackground(new Color(245, 245, 245));
        
        // 创建顶部面板
        add(createTopPanel(), BorderLayout.NORTH);
        
        // 创建选项卡面板
        createTabbedPane();
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    /**
     * 创建顶部面板
     */
    private JPanel createTopPanel() {
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(new Color(52, 152, 219));
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
        
        // 标题和用户信息
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        leftPanel.setOpaque(false);
        
        JLabel titleLabel = new JLabel("管理员控制台");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 20));
        titleLabel.setForeground(Color.WHITE);
        leftPanel.add(titleLabel);
        
        if (mainFrame.getAdminService().isLoggedIn()) {
            JLabel userLabel = new JLabel(" - " + mainFrame.getAdminService().getCurrentAdmin().getName());
            userLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            userLabel.setForeground(Color.WHITE);
            leftPanel.add(userLabel);
        }
        
        // 操作按钮
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        rightPanel.setOpaque(false);
        
        JButton logoutButton = new JButton("退出登录");
        logoutButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        logoutButton.setBackground(new Color(231, 76, 60));
        logoutButton.setForeground(Color.WHITE);
        logoutButton.setFocusPainted(false);
        logoutButton.setBorderPainted(false);
        logoutButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        logoutButton.addActionListener(e -> logout());
        rightPanel.add(logoutButton);
        
        topPanel.add(leftPanel, BorderLayout.WEST);
        topPanel.add(rightPanel, BorderLayout.EAST);
        
        return topPanel;
    }
    
    /**
     * 创建选项卡面板
     */
    private void createTabbedPane() {
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        
        // 菜品管理选项卡
        tabbedPane.addTab("菜品管理", createDishManagementPanel());
        
        // 订单管理选项卡
        tabbedPane.addTab("订单管理", createOrderManagementPanel());
        
        // 销售统计选项卡
        tabbedPane.addTab("销售统计", createStatisticsPanel());
    }
    
    /**
     * 创建菜品管理面板
     */
    private JPanel createDishManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton addButton = new JButton("添加菜品");
        JButton editButton = new JButton("编辑菜品");
        JButton deleteButton = new JButton("删除菜品");
        JButton refreshButton = new JButton("刷新");
        
        addButton.addActionListener(e -> addDish());
        editButton.addActionListener(e -> editDish());
        deleteButton.addActionListener(e -> deleteDish());
        refreshButton.addActionListener(e -> refreshDishTable());
        
        toolBar.add(addButton);
        toolBar.add(editButton);
        toolBar.add(deleteButton);
        toolBar.add(refreshButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 菜品表格
        String[] columns = {"ID", "名称", "分类", "价格", "折扣", "现价", "描述", "状态"};
        dishTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        dishTable = new JTable(dishTableModel);
        dishTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane scrollPane = new JScrollPane(dishTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 加载数据
        refreshDishTable();
        
        return panel;
    }
    
    /**
     * 创建订单管理面板
     */
    private JPanel createOrderManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton viewButton = new JButton("查看详情");
        JButton deleteButton = new JButton("删除订单");
        JButton refreshButton = new JButton("刷新");
        
        viewButton.addActionListener(e -> viewOrderDetails());
        deleteButton.addActionListener(e -> deleteOrder());
        refreshButton.addActionListener(e -> refreshOrderTable());
        
        toolBar.add(viewButton);
        toolBar.add(deleteButton);
        toolBar.add(refreshButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 订单表格
        String[] columns = {"订单号", "类型", "下单时间", "总金额", "客户电话"};
        orderTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        orderTable = new JTable(orderTableModel);
        orderTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane scrollPane = new JScrollPane(orderTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 加载数据
        refreshOrderTable();
        
        return panel;
    }
    
    /**
     * 创建统计面板
     */
    private JPanel createStatisticsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel dateLabel = new JLabel("选择日期:");
        JTextField dateField = new JTextField(LocalDate.now().toString(), 10);
        JButton generateButton = new JButton("生成报告");
        
        generateButton.addActionListener(e -> generateStatistics(dateField.getText()));
        
        toolBar.add(dateLabel);
        toolBar.add(dateField);
        toolBar.add(generateButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 统计文本区域
        statisticsArea = new JTextArea();
        statisticsArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        statisticsArea.setEditable(false);
        statisticsArea.setBackground(Color.WHITE);
        
        JScrollPane scrollPane = new JScrollPane(statisticsArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 默认生成今日统计
        generateStatistics(LocalDate.now().toString());
        
        return panel;
    }
    
    /**
     * 刷新菜品表格
     */
    private void refreshDishTable() {
        dishTableModel.setRowCount(0);
        List<Dish> dishes = dishService.getAllDishes();
        
        for (Dish dish : dishes) {
            Object[] row = {
                dish.getId(),
                dish.getName(),
                dish.getCategory(),
                "¥" + dish.getPrice(),
                dish.getDiscount().toString(),
                "¥" + dish.getDiscountedPrice(),
                dish.getDescription(),
                dish.isAvailable() ? "可用" : "不可用"
            };
            dishTableModel.addRow(row);
        }
    }
    
    /**
     * 刷新订单表格
     */
    private void refreshOrderTable() {
        orderTableModel.setRowCount(0);
        List<Order> orders = orderService.getTodayOrders();
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
        for (Order order : orders) {
            Object[] row = {
                order.getOrderId(),
                order.getOrderType().getDescription(),
                order.getOrderTime().format(formatter),
                "¥" + order.getTotalAmount(),
                order.getCustomerPhone() != null ? order.getCustomerPhone() : "-"
            };
            orderTableModel.addRow(row);
        }
    }
    
    /**
     * 添加菜品
     */
    private void addDish() {
        JOptionPane.showMessageDialog(this, "添加菜品功能待实现", "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 编辑菜品
     */
    private void editDish() {
        JOptionPane.showMessageDialog(this, "编辑菜品功能待实现", "提示", JOptionPane.INFORMATION_MESSAGE);
    }
    
    /**
     * 删除菜品
     */
    private void deleteDish() {
        int selectedRow = dishTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要删除的菜品", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String dishId = (String) dishTableModel.getValueAt(selectedRow, 0);
        int result = JOptionPane.showConfirmDialog(this, 
            "确定要删除这个菜品吗？", "确认删除", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (dishService.deleteDish(dishId)) {
                JOptionPane.showMessageDialog(this, "菜品删除成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                refreshDishTable();
            } else {
                JOptionPane.showMessageDialog(this, "菜品删除失败", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 查看订单详情
     */
    private void viewOrderDetails() {
        int selectedRow = orderTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要查看的订单", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String orderId = (String) orderTableModel.getValueAt(selectedRow, 0);
        orderService.findOrderById(orderId).ifPresentOrElse(
            order -> {
                JTextArea textArea = new JTextArea(order.getOrderDetails());
                textArea.setEditable(false);
                textArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
                JScrollPane scrollPane = new JScrollPane(textArea);
                scrollPane.setPreferredSize(new Dimension(400, 300));
                JOptionPane.showMessageDialog(this, scrollPane, "订单详情", JOptionPane.INFORMATION_MESSAGE);
            },
            () -> JOptionPane.showMessageDialog(this, "订单不存在", "错误", JOptionPane.ERROR_MESSAGE)
        );
    }
    
    /**
     * 删除订单
     */
    private void deleteOrder() {
        int selectedRow = orderTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要删除的订单", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String orderId = (String) orderTableModel.getValueAt(selectedRow, 0);
        int result = JOptionPane.showConfirmDialog(this, 
            "确定要删除这个订单吗？", "确认删除", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (orderService.deleteOrder(orderId)) {
                JOptionPane.showMessageDialog(this, "订单删除成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                refreshOrderTable();
            } else {
                JOptionPane.showMessageDialog(this, "订单删除失败", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 生成统计报告
     */
    private void generateStatistics(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr);
            String report = statisticsService.generateSalesReport(date);
            statisticsArea.setText(report);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "日期格式错误，请使用 yyyy-MM-dd 格式", 
                "错误", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 退出登录
     */
    private void logout() {
        int result = JOptionPane.showConfirmDialog(this, 
            "确定要退出登录吗？", "确认退出", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            mainFrame.getAdminService().logout();
            mainFrame.showWelcomePanel();
        }
    }
}
