package com.restaurant.ui;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.Frame;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.JButton;
import javax.swing.JFileChooser;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTabbedPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SwingUtilities;
import javax.swing.table.DefaultTableModel;

import com.restaurant.model.Dish;
import com.restaurant.model.Order;
import com.restaurant.service.DishService;
import com.restaurant.service.OrderService;
import com.restaurant.service.SalesStatisticsService;

/**
 * 管理员管理面板
 */
public class AdminManagementPanel extends JPanel {
    
    private final MainFrame mainFrame;
    private final DishService dishService;
    private final OrderService orderService;
    private final SalesStatisticsService statisticsService;
    
    private JTabbedPane tabbedPane;
    private JTable dishTable;
    private DefaultTableModel dishTableModel;
    private JTable orderTable;
    private DefaultTableModel orderTableModel;
    private JTextArea statisticsArea;
    
    public AdminManagementPanel(MainFrame mainFrame) {
        this.mainFrame = mainFrame;
        this.dishService = new DishService();
        this.orderService = new OrderService();
        this.statisticsService = new SalesStatisticsService();
        initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        setBackground(new Color(245, 245, 245));
        
        // 创建顶部面板
        add(createTopPanel(), BorderLayout.NORTH);
        
        // 创建选项卡面板
        createTabbedPane();
        add(tabbedPane, BorderLayout.CENTER);
    }
    
    /**
     * 创建顶部面板
     */
    private JPanel createTopPanel() {
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(new Color(52, 152, 219));
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
        
        // 标题和用户信息
        JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        leftPanel.setOpaque(false);
        
        JLabel titleLabel = new JLabel("管理员控制台");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 20));
        titleLabel.setForeground(Color.WHITE);
        leftPanel.add(titleLabel);
        
        if (mainFrame.getAdminService().isLoggedIn()) {
            JLabel userLabel = new JLabel(" - " + mainFrame.getAdminService().getCurrentAdmin().getName());
            userLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
            userLabel.setForeground(Color.WHITE);
            leftPanel.add(userLabel);
        }
        
        // 操作按钮
        JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.RIGHT));
        rightPanel.setOpaque(false);

        JButton profileButton = new JButton("个人信息");
        profileButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        profileButton.setBackground(new Color(52, 152, 219));
        profileButton.setForeground(Color.WHITE);
        profileButton.setFocusPainted(false);
        profileButton.setBorderPainted(false);
        profileButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        profileButton.addActionListener(e -> showProfileDialog());
        rightPanel.add(profileButton);

        JButton logoutButton = new JButton("退出登录");
        logoutButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        logoutButton.setBackground(new Color(231, 76, 60));
        logoutButton.setForeground(Color.WHITE);
        logoutButton.setFocusPainted(false);
        logoutButton.setBorderPainted(false);
        logoutButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        logoutButton.addActionListener(e -> logout());
        rightPanel.add(logoutButton);
        
        topPanel.add(leftPanel, BorderLayout.WEST);
        topPanel.add(rightPanel, BorderLayout.EAST);
        
        return topPanel;
    }
    
    /**
     * 创建选项卡面板
     */
    private void createTabbedPane() {
        tabbedPane = new JTabbedPane();
        tabbedPane.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        
        // 菜品管理选项卡
        tabbedPane.addTab("菜品管理", createDishManagementPanel());
        
        // 订单管理选项卡
        tabbedPane.addTab("订单管理", createOrderManagementPanel());
        
        // 销售统计选项卡
        tabbedPane.addTab("销售统计", createStatisticsPanel());

        // 系统管理选项卡
        tabbedPane.addTab("系统管理", createSystemManagementPanel());
    }
    
    /**
     * 创建菜品管理面板
     */
    private JPanel createDishManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton addButton = new JButton("添加菜品");
        JButton editButton = new JButton("编辑菜品");
        JButton deleteButton = new JButton("删除菜品");
        JButton refreshButton = new JButton("刷新");
        
        addButton.addActionListener(e -> addDish());
        editButton.addActionListener(e -> editDish());
        deleteButton.addActionListener(e -> deleteDish());
        refreshButton.addActionListener(e -> refreshDishTable());
        
        toolBar.add(addButton);
        toolBar.add(editButton);
        toolBar.add(deleteButton);
        toolBar.add(refreshButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 菜品表格
        String[] columns = {"ID", "名称", "分类", "价格", "折扣", "现价", "描述", "状态"};
        dishTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        dishTable = new JTable(dishTableModel);
        dishTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane scrollPane = new JScrollPane(dishTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 加载数据
        refreshDishTable();
        
        return panel;
    }
    
    /**
     * 创建订单管理面板
     */
    private JPanel createOrderManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JButton viewButton = new JButton("查看详情");
        JButton deleteButton = new JButton("删除订单");
        JButton refreshButton = new JButton("刷新");
        
        viewButton.addActionListener(e -> viewOrderDetails());
        deleteButton.addActionListener(e -> deleteOrder());
        refreshButton.addActionListener(e -> refreshOrderTable());
        
        toolBar.add(viewButton);
        toolBar.add(deleteButton);
        toolBar.add(refreshButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 订单表格
        String[] columns = {"订单号", "类型", "下单时间", "总金额", "客户电话"};
        orderTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        orderTable = new JTable(orderTableModel);
        orderTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane scrollPane = new JScrollPane(orderTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 加载数据
        refreshOrderTable();
        
        return panel;
    }
    
    /**
     * 创建统计面板
     */
    private JPanel createStatisticsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 工具栏
        JPanel toolBar = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel dateLabel = new JLabel("选择日期:");
        JTextField dateField = new JTextField(LocalDate.now().toString(), 10);
        JButton generateButton = new JButton("生成报告");
        JButton advancedButton = new JButton("高级统计");

        generateButton.addActionListener(e -> generateStatistics(dateField.getText()));
        advancedButton.addActionListener(e -> showAdvancedStatistics());

        toolBar.add(dateLabel);
        toolBar.add(dateField);
        toolBar.add(generateButton);
        toolBar.add(advancedButton);
        panel.add(toolBar, BorderLayout.NORTH);
        
        // 统计文本区域
        statisticsArea = new JTextArea();
        statisticsArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        statisticsArea.setEditable(false);
        statisticsArea.setBackground(Color.WHITE);
        
        JScrollPane scrollPane = new JScrollPane(statisticsArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        // 默认生成今日统计
        generateStatistics(LocalDate.now().toString());
        
        return panel;
    }

    /**
     * 创建系统管理面板
     */
    private JPanel createSystemManagementPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));

        // 创建功能按钮面板
        JPanel buttonPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        gbc.fill = GridBagConstraints.HORIZONTAL;

        // 系统设置按钮
        JButton settingsButton = new JButton("系统设置");
        settingsButton.setPreferredSize(new Dimension(150, 40));
        settingsButton.addActionListener(e -> showSystemSettings());
        gbc.gridx = 0; gbc.gridy = 0;
        buttonPanel.add(settingsButton, gbc);

        // 数据备份按钮
        JButton backupButton = new JButton("数据备份");
        backupButton.setPreferredSize(new Dimension(150, 40));
        backupButton.addActionListener(e -> performDataBackup());
        gbc.gridx = 1; gbc.gridy = 0;
        buttonPanel.add(backupButton, gbc);

        // 数据恢复按钮
        JButton restoreButton = new JButton("数据恢复");
        restoreButton.setPreferredSize(new Dimension(150, 40));
        restoreButton.addActionListener(e -> performDataRestore());
        gbc.gridx = 0; gbc.gridy = 1;
        buttonPanel.add(restoreButton, gbc);

        // 清理数据按钮
        JButton cleanButton = new JButton("清理数据");
        cleanButton.setPreferredSize(new Dimension(150, 40));
        cleanButton.addActionListener(e -> performDataClean());
        gbc.gridx = 1; gbc.gridy = 1;
        buttonPanel.add(cleanButton, gbc);

        // 系统信息按钮
        JButton infoButton = new JButton("系统信息");
        infoButton.setPreferredSize(new Dimension(150, 40));
        infoButton.addActionListener(e -> showSystemInfo());
        gbc.gridx = 0; gbc.gridy = 2; gbc.gridwidth = 2;
        buttonPanel.add(infoButton, gbc);

        panel.add(buttonPanel, BorderLayout.CENTER);

        // 添加说明文本
        JTextArea infoArea = new JTextArea();
        infoArea.setText("系统管理功能说明：\n\n" +
                        "• 系统设置：配置系统参数\n" +
                        "• 数据备份：备份重要数据\n" +
                        "• 数据恢复：从备份恢复数据\n" +
                        "• 清理数据：清理过期数据\n" +
                        "• 系统信息：查看系统运行状态");
        infoArea.setEditable(false);
        infoArea.setBackground(panel.getBackground());
        infoArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        infoArea.setBorder(BorderFactory.createTitledBorder("功能说明"));

        panel.add(infoArea, BorderLayout.SOUTH);

        return panel;
    }

    /**
     * 刷新菜品表格
     */
    private void refreshDishTable() {
        dishTableModel.setRowCount(0);
        List<Dish> dishes = dishService.getAllDishes();
        
        for (Dish dish : dishes) {
            Object[] row = {
                dish.getId(),
                dish.getName(),
                dish.getCategory(),
                "¥" + dish.getPrice(),
                dish.getDiscount().toString(),
                "¥" + dish.getDiscountedPrice(),
                dish.getDescription(),
                dish.isAvailable() ? "可用" : "不可用"
            };
            dishTableModel.addRow(row);
        }
    }
    
    /**
     * 刷新订单表格
     */
    private void refreshOrderTable() {
        orderTableModel.setRowCount(0);
        List<Order> orders = orderService.getTodayOrders();
        
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM-dd HH:mm");
        for (Order order : orders) {
            Object[] row = {
                order.getOrderId(),
                order.getOrderType().getDescription(),
                order.getOrderTime().format(formatter),
                "¥" + order.getTotalAmount(),
                order.getCustomerPhone() != null ? order.getCustomerPhone() : "-"
            };
            orderTableModel.addRow(row);
        }
    }
    
    /**
     * 添加菜品
     */
    private void addDish() {
        DishEditDialog dialog = new DishEditDialog((Frame) SwingUtilities.getWindowAncestor(this), dishService, null);
        dialog.setVisible(true);

        if (dialog.isConfirmed()) {
            refreshDishTable();
            JOptionPane.showMessageDialog(this, "菜品添加成功", "成功", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 编辑菜品
     */
    private void editDish() {
        int selectedRow = dishTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要编辑的菜品", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String dishId = (String) dishTableModel.getValueAt(selectedRow, 0);
        dishService.findDishById(dishId).ifPresentOrElse(
            dish -> {
                DishEditDialog dialog = new DishEditDialog((Frame) SwingUtilities.getWindowAncestor(this), dishService, dish);
                dialog.setVisible(true);

                if (dialog.isConfirmed()) {
                    refreshDishTable();
                    JOptionPane.showMessageDialog(this, "菜品更新成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                }
            },
            () -> JOptionPane.showMessageDialog(this, "菜品不存在", "错误", JOptionPane.ERROR_MESSAGE)
        );
    }
    
    /**
     * 删除菜品
     */
    private void deleteDish() {
        int selectedRow = dishTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要删除的菜品", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String dishId = (String) dishTableModel.getValueAt(selectedRow, 0);
        int result = JOptionPane.showConfirmDialog(this, 
            "确定要删除这个菜品吗？", "确认删除", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (dishService.deleteDish(dishId)) {
                JOptionPane.showMessageDialog(this, "菜品删除成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                refreshDishTable();
            } else {
                JOptionPane.showMessageDialog(this, "菜品删除失败", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 查看订单详情
     */
    private void viewOrderDetails() {
        int selectedRow = orderTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要查看的订单", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String orderId = (String) orderTableModel.getValueAt(selectedRow, 0);
        orderService.findOrderById(orderId).ifPresentOrElse(
            order -> {
                OrderDetailDialog dialog = new OrderDetailDialog((Frame) SwingUtilities.getWindowAncestor(this), order);
                dialog.setVisible(true);
            },
            () -> JOptionPane.showMessageDialog(this, "订单不存在", "错误", JOptionPane.ERROR_MESSAGE)
        );
    }
    
    /**
     * 删除订单
     */
    private void deleteOrder() {
        int selectedRow = orderTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要删除的订单", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }
        
        String orderId = (String) orderTableModel.getValueAt(selectedRow, 0);
        int result = JOptionPane.showConfirmDialog(this, 
            "确定要删除这个订单吗？", "确认删除", 
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
        
        if (result == JOptionPane.YES_OPTION) {
            if (orderService.deleteOrder(orderId)) {
                JOptionPane.showMessageDialog(this, "订单删除成功", "成功", JOptionPane.INFORMATION_MESSAGE);
                refreshOrderTable();
            } else {
                JOptionPane.showMessageDialog(this, "订单删除失败", "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 生成统计报告
     */
    private void generateStatistics(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr);
            String report = statisticsService.generateSalesReport(date);
            statisticsArea.setText(report);
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, "日期格式错误，请使用 yyyy-MM-dd 格式",
                "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 显示高级统计对话框
     */
    private void showAdvancedStatistics() {
        SalesStatisticsDialog dialog = new SalesStatisticsDialog((Frame) SwingUtilities.getWindowAncestor(this), statisticsService);
        dialog.setVisible(true);
    }

    /**
     * 显示个人信息对话框
     */
    private void showProfileDialog() {
        AdminProfileDialog dialog = new AdminProfileDialog((Frame) SwingUtilities.getWindowAncestor(this), mainFrame.getAdminService());
        dialog.setVisible(true);
    }

    /**
     * 显示系统设置对话框
     */
    private void showSystemSettings() {
        SystemSettingsDialog dialog = new SystemSettingsDialog((Frame) SwingUtilities.getWindowAncestor(this));
        dialog.setVisible(true);
    }

    /**
     * 执行数据备份
     */
    private void performDataBackup() {
        JFileChooser fileChooser = new JFileChooser();
        fileChooser.setDialogTitle("选择备份保存位置");
        fileChooser.setSelectedFile(new java.io.File("restaurant_backup_" + java.time.LocalDate.now() + ".sql"));

        if (fileChooser.showSaveDialog(this) == JFileChooser.APPROVE_OPTION) {
            // 这里可以实现实际的数据备份逻辑
            JOptionPane.showMessageDialog(this,
                "数据备份功能待实现\n备份文件: " + fileChooser.getSelectedFile().getName(),
                "备份", JOptionPane.INFORMATION_MESSAGE);
        }
    }

    /**
     * 执行数据恢复
     */
    private void performDataRestore() {
        int result = JOptionPane.showConfirmDialog(this,
            "数据恢复将覆盖现有数据，确定要继续吗？", "确认恢复",
            JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setDialogTitle("选择备份文件");

            if (fileChooser.showOpenDialog(this) == JFileChooser.APPROVE_OPTION) {
                // 这里可以实现实际的数据恢复逻辑
                JOptionPane.showMessageDialog(this,
                    "数据恢复功能待实现\n恢复文件: " + fileChooser.getSelectedFile().getName(),
                    "恢复", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    /**
     * 执行数据清理
     */
    private void performDataClean() {
        String[] options = {"清理7天前数据", "清理30天前数据", "自定义清理", "取消"};
        int choice = JOptionPane.showOptionDialog(this,
            "请选择要清理的数据范围：", "数据清理",
            JOptionPane.DEFAULT_OPTION, JOptionPane.QUESTION_MESSAGE,
            null, options, options[3]);

        if (choice >= 0 && choice < 3) {
            int confirm = JOptionPane.showConfirmDialog(this,
                "确定要清理数据吗？此操作不可恢复！", "确认清理",
                JOptionPane.YES_NO_OPTION, JOptionPane.WARNING_MESSAGE);

            if (confirm == JOptionPane.YES_OPTION) {
                // 这里可以实现实际的数据清理逻辑
                JOptionPane.showMessageDialog(this,
                    "数据清理功能待实现", "清理", JOptionPane.INFORMATION_MESSAGE);
            }
        }
    }

    /**
     * 显示系统信息
     */
    private void showSystemInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== 系统信息 ===\n\n");
        info.append("系统名称: ").append(com.restaurant.util.SystemConfig.getFullSystemName()).append("\n");
        info.append("Java版本: ").append(System.getProperty("java.version")).append("\n");
        info.append("操作系统: ").append(System.getProperty("os.name")).append(" ").append(System.getProperty("os.version")).append("\n");
        info.append("系统架构: ").append(System.getProperty("os.arch")).append("\n");
        info.append("可用内存: ").append(Runtime.getRuntime().freeMemory() / 1024 / 1024).append(" MB\n");
        info.append("总内存: ").append(Runtime.getRuntime().totalMemory() / 1024 / 1024).append(" MB\n");
        info.append("最大内存: ").append(Runtime.getRuntime().maxMemory() / 1024 / 1024).append(" MB\n");
        info.append("当前时间: ").append(java.time.LocalDateTime.now()).append("\n");
        info.append("\n").append(com.restaurant.util.SystemConfig.getCopyright());

        JTextArea textArea = new JTextArea(info.toString());
        textArea.setEditable(false);
        textArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));

        JScrollPane scrollPane = new JScrollPane(textArea);
        scrollPane.setPreferredSize(new Dimension(400, 300));

        JOptionPane.showMessageDialog(this, scrollPane, "系统信息", JOptionPane.INFORMATION_MESSAGE);
    }

    /**
     * 退出登录
     */
    private void logout() {
        int result = JOptionPane.showConfirmDialog(this,
            "确定要退出登录吗？", "确认退出",
            JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);

        if (result == JOptionPane.YES_OPTION) {
            mainFrame.getAdminService().logout();
            mainFrame.showWelcomePanel();
        }
    }
}
