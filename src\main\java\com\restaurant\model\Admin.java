package com.restaurant.model;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 管理员实体类
 * Admin Entity Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class Admin {
    private String username;            // 用户名
    private String password;            // 密码
    private String name;                // 姓名
    private String email;               // 邮箱
    private LocalDateTime lastLoginTime; // 最后登录时间
    private boolean active;             // 是否激活
    
    /**
     * 默认构造函数
     */
    public Admin() {
        this.active = true;
    }
    
    /**
     * 带参数的构造函数
     */
    public Admin(String username, String password, String name) {
        this();
        this.username = username;
        this.password = password;
        this.name = name;
    }
    
    /**
     * 完整构造函数
     */
    public Admin(String username, String password, String name, String email, boolean active) {
        this.username = username;
        this.password = password;
        this.name = name;
        this.email = email;
        this.active = active;
    }
    
    /**
     * 验证密码
     * @param inputPassword 输入的密码
     * @return 密码是否正确
     */
    public boolean validatePassword(String inputPassword) {
        return password != null && password.equals(inputPassword);
    }
    
    /**
     * 更新最后登录时间
     */
    public void updateLastLoginTime() {
        this.lastLoginTime = LocalDateTime.now();
    }
    
    // Getter和Setter方法
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public LocalDateTime getLastLoginTime() {
        return lastLoginTime;
    }
    
    public void setLastLoginTime(LocalDateTime lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Admin admin = (Admin) o;
        return Objects.equals(username, admin.username);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(username);
    }
    
    @Override
    public String toString() {
        return String.format("Admin{username='%s', name='%s', email='%s', active=%s}",
                username, name, email, active);
    }
}
