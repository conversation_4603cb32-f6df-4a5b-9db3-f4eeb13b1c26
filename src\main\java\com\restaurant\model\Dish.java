package com.restaurant.model;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * 菜品实体类
 * Dish Entity Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class Dish {
    private String id;              // 菜品ID
    private String name;            // 菜品名称
    private BigDecimal price;       // 菜品价格
    private String category;        // 菜品分类
    private String description;     // 菜品描述
    private boolean available;      // 是否可用
    private BigDecimal discount;    // 折扣（1.0表示无折扣，0.8表示8折）
    
    /**
     * 默认构造函数
     */
    public Dish() {
        this.available = true;
        this.discount = BigDecimal.ONE;
    }
    
    /**
     * 带参数的构造函数
     */
    public Dish(String id, String name, BigDecimal price, String category) {
        this();
        this.id = id;
        this.name = name;
        this.price = price;
        this.category = category;
    }
    
    /**
     * 完整构造函数
     */
    public Dish(String id, String name, BigDecimal price, String category, 
                String description, boolean available, BigDecimal discount) {
        this.id = id;
        this.name = name;
        this.price = price;
        this.category = category;
        this.description = description;
        this.available = available;
        this.discount = discount != null ? discount : BigDecimal.ONE;
    }
    
    /**
     * 计算折扣后的价格
     * @return 折扣后的价格
     */
    public BigDecimal getDiscountedPrice() {
        return price.multiply(discount);
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isAvailable() {
        return available;
    }
    
    public void setAvailable(boolean available) {
        this.available = available;
    }
    
    public BigDecimal getDiscount() {
        return discount;
    }
    
    public void setDiscount(BigDecimal discount) {
        this.discount = discount != null ? discount : BigDecimal.ONE;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Dish dish = (Dish) o;
        return Objects.equals(id, dish.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return String.format("Dish{id='%s', name='%s', price=%s, category='%s', available=%s, discount=%s}",
                id, name, price, category, available, discount);
    }
}
