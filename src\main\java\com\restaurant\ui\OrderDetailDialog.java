package com.restaurant.ui;

import com.restaurant.model.Order;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 订单详情对话框
 */
public class OrderDetailDialog extends JDialog {
    
    private final Order order;
    
    public OrderDetailDialog(Frame parent, Order order) {
        super(parent, "订单详情 - " + order.getOrderId(), true);
        this.order = order;
        
        initializeComponents();
        setSize(500, 600);
        setLocationRelativeTo(parent);
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // 创建详情面板
        JPanel detailPanel = createDetailPanel();
        add(detailPanel, BorderLayout.CENTER);
        
        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建详情面板
     */
    private JPanel createDetailPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        // 订单详情文本区域
        JTextArea detailArea = new JTextArea();
        detailArea.setText(order.getOrderDetails());
        detailArea.setEditable(false);
        detailArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        detailArea.setBackground(Color.WHITE);
        detailArea.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        JScrollPane scrollPane = new JScrollPane(detailArea);
        scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
        scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        JButton printButton = new JButton("打印订单");
        printButton.addActionListener(new PrintActionListener());
        
        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dispose());
        
        panel.add(printButton);
        panel.add(closeButton);
        
        return panel;
    }
    
    /**
     * 打印按钮事件监听器
     */
    private class PrintActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                // 创建打印对话框
                JTextArea printArea = new JTextArea(order.getOrderDetails());
                printArea.setFont(new Font("微软雅黑", Font.PLAIN, 10));
                
                // 显示打印预览
                boolean doPrint = printArea.print();
                if (doPrint) {
                    JOptionPane.showMessageDialog(OrderDetailDialog.this, 
                        "订单打印成功", "打印", JOptionPane.INFORMATION_MESSAGE);
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(OrderDetailDialog.this, 
                    "打印失败: " + ex.getMessage(), "打印错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
}
