package com.restaurant.dao.mysql;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.Date;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.restaurant.model.DineInOrder;
import com.restaurant.model.Dish;
import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.TakeawayOrder;
import com.restaurant.util.DatabaseConfig;

/**
 * MySQL订单数据访问对象
 * MySQL Order Data Access Object
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class MySQLOrderDao {
    
    private final DatabaseConfig dbConfig;
    
    public MySQLOrderDao() {
        this.dbConfig = DatabaseConfig.getInstance();
    }
    
    /**
     * 保存订单
     */
    public boolean saveOrder(Order order) {
        Connection conn = null;
        try {
            conn = dbConfig.getConnection();
            conn.setAutoCommit(false); // 开启事务
            
            // 保存订单主表
            if (saveOrderMain(conn, order)) {
                // 保存订单项
                if (saveOrderItems(conn, order)) {
                    conn.commit();
                    return true;
                } else {
                    conn.rollback();
                    return false;
                }
            } else {
                conn.rollback();
                return false;
            }
            
        } catch (SQLException e) {
            System.err.println("保存订单失败: " + e.getMessage());
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException ex) {
                System.err.println("回滚事务失败: " + ex.getMessage());
            }
            return false;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                    conn.close();
                }
            } catch (SQLException e) {
                System.err.println("关闭连接失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 保存订单主表
     */
    private boolean saveOrderMain(Connection conn, Order order) throws SQLException {
        String sql = "INSERT INTO orders (id, order_type, order_time, total_amount, customer_phone, notes, " +
                    "table_number, has_private_room, private_room_fee, delivery_time, delivery_address, delivery_fee) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, order.getOrderId());
            stmt.setString(2, order.getOrderType().name());
            stmt.setTimestamp(3, Timestamp.valueOf(order.getOrderTime()));
            stmt.setBigDecimal(4, order.getTotalAmount());
            stmt.setString(5, order.getCustomerPhone());
            stmt.setString(6, order.getNotes());
            
            if (order instanceof DineInOrder) {
                DineInOrder dineInOrder = (DineInOrder) order;
                stmt.setString(7, dineInOrder.getTableNumber());
                stmt.setBoolean(8, dineInOrder.isHasPrivateRoom());
                stmt.setBigDecimal(9, dineInOrder.getPrivateRoomFee());
                stmt.setNull(10, Types.TIMESTAMP);
                stmt.setNull(11, Types.VARCHAR);
                stmt.setBigDecimal(12, BigDecimal.ZERO);
            } else if (order instanceof TakeawayOrder) {
                TakeawayOrder takeawayOrder = (TakeawayOrder) order;
                stmt.setNull(7, Types.VARCHAR);
                stmt.setBoolean(8, false);
                stmt.setBigDecimal(9, BigDecimal.ZERO);
                stmt.setTimestamp(10, takeawayOrder.getDeliveryTime() != null ? 
                    Timestamp.valueOf(takeawayOrder.getDeliveryTime()) : null);
                stmt.setString(11, takeawayOrder.getDeliveryAddress());
                stmt.setBigDecimal(12, takeawayOrder.getDeliveryFee());
            }
            
            return stmt.executeUpdate() > 0;
        }
    }
    
    /**
     * 保存订单项
     */
    private boolean saveOrderItems(Connection conn, Order order) throws SQLException {
        String sql = "INSERT INTO order_item (order_id, dish_id, dish_name, unit_price, quantity, total_price, notes) " +
                    "VALUES (?, ?, ?, ?, ?, ?, ?)";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            for (OrderItem item : order.getOrderItems()) {
                stmt.setString(1, order.getOrderId());
                stmt.setString(2, item.getDish().getId());
                stmt.setString(3, item.getDish().getName());
                stmt.setBigDecimal(4, item.getUnitPrice());
                stmt.setInt(5, item.getQuantity());
                stmt.setBigDecimal(6, item.getTotalPrice());
                stmt.setString(7, item.getNotes());
                stmt.addBatch();
            }
            
            int[] results = stmt.executeBatch();
            for (int result : results) {
                if (result <= 0) {
                    return false;
                }
            }
            return true;
        }
    }
    
    /**
     * 获取今日订单
     */
    public List<Order> getTodayOrders() {
        return getOrdersByDate(LocalDate.now());
    }
    
    /**
     * 获取指定日期的订单
     */
    public List<Order> getOrdersByDate(LocalDate date) {
        List<Order> orders = new ArrayList<>();
        String sql = "SELECT * FROM orders WHERE DATE(order_time) = ? ORDER BY order_time DESC";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setDate(1, Date.valueOf(date));
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Order order = mapResultSetToOrder(rs);
                    if (order != null) {
                        // 加载订单项
                        loadOrderItems(conn, order);
                        orders.add(order);
                    }
                }
            }
            
        } catch (SQLException e) {
            System.err.println("获取订单失败: " + e.getMessage());
        }
        
        return orders;
    }
    
    /**
     * 根据ID查找订单
     */
    public Optional<Order> findById(String orderId) {
        String sql = "SELECT * FROM orders WHERE id = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, orderId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    Order order = mapResultSetToOrder(rs);
                    if (order != null) {
                        loadOrderItems(conn, order);
                        return Optional.of(order);
                    }
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据ID查找订单失败: " + e.getMessage());
        }
        
        return Optional.empty();
    }
    
    /**
     * 根据手机号查找订单
     */
    public List<Order> findByPhone(String phone) {
        List<Order> orders = new ArrayList<>();
        String sql = "SELECT * FROM orders WHERE customer_phone = ? ORDER BY order_time DESC";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, phone);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    Order order = mapResultSetToOrder(rs);
                    if (order != null) {
                        loadOrderItems(conn, order);
                        orders.add(order);
                    }
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据手机号查找订单失败: " + e.getMessage());
        }
        
        return orders;
    }
    
    /**
     * 更新订单
     */
    public boolean updateOrder(Order order) {
        Connection conn = null;
        try {
            conn = dbConfig.getConnection();
            conn.setAutoCommit(false); // 开启事务

            // 删除原有订单项
            String deleteItemsSql = "DELETE FROM order_item WHERE order_id = ?";
            try (PreparedStatement stmt = conn.prepareStatement(deleteItemsSql)) {
                stmt.setString(1, order.getOrderId());
                stmt.executeUpdate();
            }

            // 更新订单主表
            String updateOrderSql = "UPDATE orders SET order_time = ?, total_amount = ?, customer_phone = ?, notes = ?, " +
                    "table_number = ?, has_private_room = ?, private_room_fee = ?, delivery_time = ?, delivery_address = ?, delivery_fee = ? " +
                    "WHERE id = ?";

            try (PreparedStatement stmt = conn.prepareStatement(updateOrderSql)) {
                stmt.setTimestamp(1, Timestamp.valueOf(order.getOrderTime()));
                stmt.setBigDecimal(2, order.getTotalAmount());
                stmt.setString(3, order.getCustomerPhone());
                stmt.setString(4, order.getNotes());

                if (order instanceof DineInOrder) {
                    DineInOrder dineInOrder = (DineInOrder) order;
                    stmt.setString(5, dineInOrder.getTableNumber());
                    stmt.setBoolean(6, dineInOrder.isHasPrivateRoom());
                    stmt.setBigDecimal(7, dineInOrder.getPrivateRoomFee());
                    stmt.setNull(8, Types.TIMESTAMP);
                    stmt.setNull(9, Types.VARCHAR);
                    stmt.setBigDecimal(10, BigDecimal.ZERO);
                } else if (order instanceof TakeawayOrder) {
                    TakeawayOrder takeawayOrder = (TakeawayOrder) order;
                    stmt.setNull(5, Types.VARCHAR);
                    stmt.setBoolean(6, false);
                    stmt.setBigDecimal(7, BigDecimal.ZERO);
                    stmt.setTimestamp(8, takeawayOrder.getDeliveryTime() != null ?
                        Timestamp.valueOf(takeawayOrder.getDeliveryTime()) : null);
                    stmt.setString(9, takeawayOrder.getDeliveryAddress());
                    stmt.setBigDecimal(10, takeawayOrder.getDeliveryFee());
                }

                stmt.setString(11, order.getOrderId());
                stmt.executeUpdate();
            }

            // 重新保存订单项
            if (saveOrderItems(conn, order)) {
                conn.commit();
                return true;
            } else {
                conn.rollback();
                return false;
            }

        } catch (SQLException e) {
            System.err.println("更新订单失败: " + e.getMessage());
            try {
                if (conn != null) {
                    conn.rollback();
                }
            } catch (SQLException ex) {
                System.err.println("回滚事务失败: " + ex.getMessage());
            }
            return false;
        } finally {
            try {
                if (conn != null) {
                    conn.setAutoCommit(true);
                    conn.close();
                }
            } catch (SQLException e) {
                System.err.println("关闭连接失败: " + e.getMessage());
            }
        }
    }

    /**
     * 删除订单
     */
    public boolean deleteOrder(String orderId) {
        String sql = "DELETE FROM orders WHERE id = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, orderId);
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("删除订单失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 加载订单项
     */
    private void loadOrderItems(Connection conn, Order order) throws SQLException {
        String sql = "SELECT * FROM order_item WHERE order_id = ?";
        
        try (PreparedStatement stmt = conn.prepareStatement(sql)) {
            stmt.setString(1, order.getOrderId());
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    // 创建简化的Dish对象
                    Dish dish = new Dish();
                    dish.setId(rs.getString("dish_id"));
                    dish.setName(rs.getString("dish_name"));
                    
                    OrderItem item = new OrderItem(
                        dish,
                        rs.getInt("quantity"),
                        rs.getBigDecimal("unit_price"),
                        rs.getString("notes")
                    );
                    
                    order.addOrderItem(item);
                }
            }
        }

        // 加载完订单项后重新计算总金额
        order.calculateTotalAmount();
    }
    
    /**
     * 将ResultSet映射为Order对象
     */
    private Order mapResultSetToOrder(ResultSet rs) throws SQLException {
        String orderType = rs.getString("order_type");
        String orderId = rs.getString("id");
        LocalDateTime orderTime = rs.getTimestamp("order_time").toLocalDateTime();
        
        Order order;
        if ("DINE_IN".equals(orderType)) {
            order = new DineInOrder(
                orderId,
                rs.getString("table_number"),
                rs.getBoolean("has_private_room"),
                rs.getBigDecimal("private_room_fee")
            );
        } else if ("TAKEAWAY".equals(orderType)) {
            Timestamp deliveryTime = rs.getTimestamp("delivery_time");
            order = new TakeawayOrder(
                orderId,
                deliveryTime != null ? deliveryTime.toLocalDateTime() : null,
                rs.getString("delivery_address"),
                rs.getString("customer_phone"),
                rs.getBigDecimal("delivery_fee")
            );
        } else {
            return null;
        }
        
        order.setOrderTime(orderTime);
        order.setCustomerPhone(rs.getString("customer_phone"));
        order.setNotes(rs.getString("notes"));
        // 注意：总金额会在加载订单项后重新计算
        
        return order;
    }
}
