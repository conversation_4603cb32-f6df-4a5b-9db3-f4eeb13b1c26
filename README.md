# 餐厅自助点餐系统

## 项目简介

这是一个基于Java Swing开发的餐厅自助点餐系统，采用面向对象的程序设计方法，实现了顾客自助点餐和管理员后台管理功能。

## 技术栈

- **开发语言**: Java 17
- **构建工具**: Maven
- **UI框架**: Swing
- **数据存储**: JSON文件
- **JSON处理**: Jackson

## 功能特性

### 顾客功能
- 浏览菜单（支持分类筛选和搜索）
- 自助点餐（在店消费/外卖）
- 查看订单详情和总金额
- 提交订单并生成订单号

### 管理员功能
- 管理员登录验证
- 菜品管理（增删改查、设置折扣）
- 订单管理（查看、删除订单）
- 销售统计（按日期统计销售情况）

### 系统特性
- 订单按日期自动分文件存储
- 支持在店消费（餐桌号、包厢费）
- 支持外卖订单（送餐地址、外卖费）
- 实时计算订单金额（含折扣）
- 销售数据统计和报表生成

## 项目结构

```
src/main/java/com/restaurant/
├── RestaurantApplication.java          # 主程序入口
├── model/                              # 实体类
│   ├── Admin.java                      # 管理员实体
│   ├── Dish.java                       # 菜品实体
│   ├── Order.java                      # 订单抽象基类
│   ├── DineInOrder.java               # 在店消费订单
│   ├── TakeawayOrder.java             # 外卖订单
│   ├── OrderItem.java                 # 订单项
│   └── OrderType.java                 # 订单类型枚举
├── dao/                               # 数据访问层
│   ├── AdminDao.java                  # 管理员数据访问
│   ├── DishDao.java                   # 菜品数据访问
│   └── OrderDao.java                  # 订单数据访问
├── service/                           # 业务逻辑层
│   ├── AdminService.java              # 管理员服务
│   ├── DishService.java               # 菜品服务
│   ├── OrderService.java              # 订单服务
│   └── SalesStatisticsService.java    # 销售统计服务
├── ui/                                # 用户界面
│   ├── MainFrame.java                 # 主窗口
│   ├── LoginPanel.java                # 登录面板
│   ├── CustomerOrderPanel.java        # 顾客点餐面板
│   └── AdminManagementPanel.java      # 管理员管理面板
└── util/                              # 工具类
    ├── FileUtil.java                  # 文件工具
    ├── JsonUtil.java                  # JSON工具
    └── OrderIdGenerator.java          # 订单ID生成器
```

## 运行方式

### 使用Maven运行
```bash
mvn compile exec:java
```

### 直接运行主类
```bash
java -cp target/classes com.restaurant.RestaurantApplication
```

## 默认账户

- **管理员用户名**: admin
- **管理员密码**: 123456

## 数据文件

系统运行后会在项目根目录下创建 `data` 文件夹：

```
data/
├── menu.json                          # 菜单数据
├── admin.json                         # 管理员数据
└── orders/                            # 订单数据目录
    ├── orders_2024-06-24.json        # 按日期存储的订单文件
    └── ...
```

## 使用说明

### 顾客点餐流程
1. 启动系统，点击"顾客点餐"
2. 浏览菜单，可按分类筛选或搜索菜品
3. 选择菜品，设置数量，点击"添加到订单"
4. 选择订单类型（在店消费/外卖）
5. 填写相关信息（餐桌号/送餐地址等）
6. 确认订单信息，点击"提交订单"
7. 系统生成订单号并显示订单详情

### 管理员管理流程
1. 启动系统，点击"管理员登录"
2. 输入用户名和密码登录
3. 在管理控制台中可以：
   - 管理菜品：添加、编辑、删除菜品，设置折扣
   - 管理订单：查看订单详情，删除订单
   - 查看统计：生成指定日期的销售报告

## 系统特点

1. **面向对象设计**: 采用继承、多态等OOP特性，订单类型通过继承实现
2. **分层架构**: 清晰的分层结构，便于维护和扩展
3. **数据持久化**: 使用JSON文件存储，支持数据的持久化保存
4. **用户友好**: 直观的Swing界面，操作简单易懂
5. **功能完整**: 涵盖点餐、管理、统计等完整业务流程

## 扩展功能

系统预留了扩展接口，可以方便地添加以下功能：
- 会员管理系统
- 优惠券功能
- 库存管理
- 打印功能
- 数据库支持
- 网络版本

## 注意事项

1. 确保系统已安装Java 17或更高版本
2. 首次运行会自动创建默认数据
3. 数据文件请勿手动修改，以免造成数据损坏
4. 建议定期备份data目录下的数据文件

## 开发信息

- **开发语言**: Java
- **开发工具**: 支持Maven的IDE（如IntelliJ IDEA、Eclipse等）
- **编码规范**: 遵循Java编码规范，包含完整的中英文注释
- **设计模式**: 使用了工厂模式、单例模式等设计模式

---

© 2024 Restaurant Self-Ordering System v1.0
