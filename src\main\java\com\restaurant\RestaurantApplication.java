package com.restaurant;

import javax.swing.JOptionPane;
import javax.swing.SwingUtilities;
import javax.swing.UIManager;

import com.restaurant.ui.MainFrame;
import com.restaurant.util.DatabaseInitializer;
import com.restaurant.util.FileUtil;

/**
 * 餐厅自助点餐系统主应用程序
 * Restaurant Self-Ordering System Main Application
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class RestaurantApplication {
    
    public static void main(String[] args) {
        System.out.println("正在启动餐厅自助点餐系统...");

        // 初始化数据库
        DatabaseInitializer dbInitializer = new DatabaseInitializer();
        if (!dbInitializer.setupDatabase()) {
            JOptionPane.showMessageDialog(null,
                "数据库初始化失败！\n请检查MySQL配置和连接。\n\n" +
                "请确保：\n" +
                "1. MySQL服务已启动\n" +
                "2. 数据库配置正确（src/main/resources/database.properties）\n" +
                "3. 用户有创建数据库的权限",
                "数据库错误", JOptionPane.ERROR_MESSAGE);
            System.exit(1);
        }

        // 初始化文件目录（作为备用）
        FileUtil.initializeDataDirectories();

        // 设置系统外观
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
        } catch (Exception e) {
            System.err.println("无法设置系统外观: " + e.getMessage());
        }

        // 在事件调度线程中启动GUI
        SwingUtilities.invokeLater(() -> {
            try {
                System.out.println("启动用户界面...");
                new MainFrame().setVisible(true);
                System.out.println("系统启动完成！");
            } catch (Exception e) {
                System.err.println("启动应用程序时发生错误: " + e.getMessage());
                e.printStackTrace();
                JOptionPane.showMessageDialog(null,
                    "启动应用程序失败: " + e.getMessage(),
                    "启动错误", JOptionPane.ERROR_MESSAGE);
            }
        });
    }
}
