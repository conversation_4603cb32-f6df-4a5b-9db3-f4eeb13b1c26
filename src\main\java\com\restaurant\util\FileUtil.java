package com.restaurant.util;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * 文件工具类
 * File Utility Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class FileUtil {
    
    // 数据目录
    public static final String DATA_DIR = "data";
    public static final String MENU_FILE = DATA_DIR + "/menu.json";
    public static final String ADMIN_FILE = DATA_DIR + "/admin.json";
    public static final String ORDERS_DIR = DATA_DIR + "/orders";
    
    /**
     * 初始化数据目录
     */
    public static void initializeDataDirectories() {
        try {
            // 创建数据目录
            Path dataPath = Paths.get(DATA_DIR);
            if (!Files.exists(dataPath)) {
                Files.createDirectories(dataPath);
            }
            
            // 创建订单目录
            Path ordersPath = Paths.get(ORDERS_DIR);
            if (!Files.exists(ordersPath)) {
                Files.createDirectories(ordersPath);
            }
            
        } catch (IOException e) {
            System.err.println("初始化数据目录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取今日订单文件路径
     * @return 今日订单文件路径
     */
    public static String getTodayOrdersFile() {
        String today = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return ORDERS_DIR + "/orders_" + today + ".json";
    }
    
    /**
     * 获取指定日期的订单文件路径
     * @param date 日期
     * @return 订单文件路径
     */
    public static String getOrdersFileByDate(LocalDate date) {
        String dateStr = date.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        return ORDERS_DIR + "/orders_" + dateStr + ".json";
    }
    
    /**
     * 检查文件是否存在
     * @param filePath 文件路径
     * @return 文件是否存在
     */
    public static boolean fileExists(String filePath) {
        return Files.exists(Paths.get(filePath));
    }
    
    /**
     * 读取文件内容
     * @param filePath 文件路径
     * @return 文件内容
     * @throws IOException IO异常
     */
    public static String readFile(String filePath) throws IOException {
        if (!fileExists(filePath)) {
            return null;
        }
        return Files.readString(Paths.get(filePath));
    }
    
    /**
     * 写入文件内容
     * @param filePath 文件路径
     * @param content 文件内容
     * @throws IOException IO异常
     */
    public static void writeFile(String filePath, String content) throws IOException {
        Path path = Paths.get(filePath);
        // 确保父目录存在
        Path parentDir = path.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }
        Files.writeString(path, content);
    }
    
    /**
     * 备份文件
     * @param filePath 原文件路径
     * @return 备份文件路径
     * @throws IOException IO异常
     */
    public static String backupFile(String filePath) throws IOException {
        if (!fileExists(filePath)) {
            return null;
        }
        
        String backupPath = filePath + ".backup." + System.currentTimeMillis();
        Files.copy(Paths.get(filePath), Paths.get(backupPath));
        return backupPath;
    }
    
    /**
     * 删除文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public static boolean deleteFile(String filePath) {
        try {
            return Files.deleteIfExists(Paths.get(filePath));
        } catch (IOException e) {
            System.err.println("删除文件失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取文件大小
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    public static long getFileSize(String filePath) {
        try {
            if (fileExists(filePath)) {
                return Files.size(Paths.get(filePath));
            }
        } catch (IOException e) {
            System.err.println("获取文件大小失败: " + e.getMessage());
        }
        return 0;
    }
}
