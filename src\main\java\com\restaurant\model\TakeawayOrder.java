package com.restaurant.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 外卖订单类
 * Takeaway Order Class
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class TakeawayOrder extends Order {
    private LocalDateTime deliveryTime;    // 送餐时间
    private String deliveryAddress;        // 送餐地址
    private BigDecimal deliveryFee;        // 外卖服务费
    
    /**
     * 默认构造函数
     */
    public TakeawayOrder() {
        super();
        this.orderType = OrderType.TAKEAWAY;
        this.deliveryFee = BigDecimal.ZERO;
    }
    
    /**
     * 带参数的构造函数
     */
    public TakeawayOrder(String orderId, String deliveryAddress, String customerPhone) {
        super(orderId, OrderType.TAKEAWAY);
        this.deliveryAddress = deliveryAddress;
        this.customerPhone = customerPhone;
        this.deliveryFee = BigDecimal.ZERO;
    }
    
    /**
     * 完整构造函数
     */
    public TakeawayOrder(String orderId, LocalDateTime deliveryTime, String deliveryAddress, 
                        String customerPhone, BigDecimal deliveryFee) {
        super(orderId, OrderType.TAKEAWAY);
        this.deliveryTime = deliveryTime;
        this.deliveryAddress = deliveryAddress;
        this.customerPhone = customerPhone;
        this.deliveryFee = deliveryFee != null ? deliveryFee : BigDecimal.ZERO;
    }
    
    @Override
    public void calculateTotalAmount() {
        BigDecimal itemsSubtotal = calculateItemsSubtotal();
        this.totalAmount = itemsSubtotal.add(deliveryFee);
    }
    
    @Override
    public String getOrderDetails() {
        StringBuilder details = new StringBuilder();
        details.append("=== 外卖订单 ===\n");
        details.append("订单编号: ").append(orderId).append("\n");
        details.append("下单时间: ").append(orderTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        details.append("联系电话: ").append(customerPhone).append("\n");
        details.append("送餐地址: ").append(deliveryAddress).append("\n");
        
        if (deliveryTime != null) {
            details.append("送餐时间: ").append(deliveryTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n");
        }
        
        details.append("外卖服务费: ").append(deliveryFee).append("元\n");
        
        details.append("\n--- 订单明细 ---\n");
        for (OrderItem item : orderItems) {
            details.append(String.format("%s x%d = %.2f元\n", 
                    item.getDish().getName(), 
                    item.getQuantity(), 
                    item.getTotalPrice()));
        }
        
        details.append("\n小计: ").append(calculateItemsSubtotal()).append("元\n");
        details.append("外卖服务费: ").append(deliveryFee).append("元\n");
        details.append("总计: ").append(totalAmount).append("元\n");
        
        if (notes != null && !notes.trim().isEmpty()) {
            details.append("备注: ").append(notes).append("\n");
        }
        
        return details.toString();
    }
    
    // Getter和Setter方法
    public LocalDateTime getDeliveryTime() {
        return deliveryTime;
    }
    
    public void setDeliveryTime(LocalDateTime deliveryTime) {
        this.deliveryTime = deliveryTime;
    }
    
    public String getDeliveryAddress() {
        return deliveryAddress;
    }
    
    public void setDeliveryAddress(String deliveryAddress) {
        this.deliveryAddress = deliveryAddress;
    }
    
    public BigDecimal getDeliveryFee() {
        return deliveryFee;
    }
    
    public void setDeliveryFee(BigDecimal deliveryFee) {
        this.deliveryFee = deliveryFee != null ? deliveryFee : BigDecimal.ZERO;
        calculateTotalAmount();
    }
    
    @Override
    public String toString() {
        return String.format("TakeawayOrder{orderId='%s', deliveryAddress='%s', customerPhone='%s', totalAmount=%s}",
                orderId, deliveryAddress, customerPhone, totalAmount);
    }
}
