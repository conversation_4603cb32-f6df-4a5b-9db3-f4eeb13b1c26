package com.restaurant.ui;

import com.restaurant.service.SalesStatisticsService;

import javax.swing.*;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 销售统计对话框
 * Sales Statistics Dialog
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class SalesStatisticsDialog extends JDialog {
    
    private final SalesStatisticsService statisticsService;
    
    // UI组件
    private JTextField startDateField;
    private JTextField endDateField;
    private JTextArea summaryArea;
    private JTable dishSalesTable;
    private DefaultTableModel dishSalesTableModel;
    
    public SalesStatisticsDialog(Frame parent, SalesStatisticsService statisticsService) {
        super(parent, "销售统计分析", true);
        this.statisticsService = statisticsService;
        
        initializeComponents();
        setSize(800, 600);
        setLocationRelativeTo(parent);
        
        // 默认显示今日统计
        generateTodayStatistics();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // 创建顶部查询面板
        add(createQueryPanel(), BorderLayout.NORTH);
        
        // 创建中央内容面板
        add(createContentPanel(), BorderLayout.CENTER);
        
        // 创建底部按钮面板
        add(createButtonPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * 创建查询面板
     */
    private JPanel createQueryPanel() {
        JPanel panel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        panel.setBorder(BorderFactory.createTitledBorder("查询条件"));
        
        panel.add(new JLabel("开始日期:"));
        startDateField = new JTextField(LocalDate.now().toString(), 10);
        panel.add(startDateField);
        
        panel.add(new JLabel("结束日期:"));
        endDateField = new JTextField(LocalDate.now().toString(), 10);
        panel.add(endDateField);
        
        JButton queryButton = new JButton("查询");
        queryButton.addActionListener(new QueryActionListener());
        panel.add(queryButton);
        
        JButton todayButton = new JButton("今日统计");
        todayButton.addActionListener(e -> generateTodayStatistics());
        panel.add(todayButton);
        
        return panel;
    }
    
    /**
     * 创建内容面板
     */
    private JPanel createContentPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        
        // 创建选项卡面板
        JTabbedPane tabbedPane = new JTabbedPane();
        
        // 总体统计选项卡
        tabbedPane.addTab("总体统计", createSummaryPanel());
        
        // 菜品销售选项卡
        tabbedPane.addTab("菜品销售", createDishSalesPanel());
        
        panel.add(tabbedPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建总体统计面板
     */
    private JPanel createSummaryPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        summaryArea = new JTextArea();
        summaryArea.setEditable(false);
        summaryArea.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        summaryArea.setBackground(Color.WHITE);
        
        JScrollPane scrollPane = new JScrollPane(summaryArea);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建菜品销售面板
     */
    private JPanel createDishSalesPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 创建表格
        String[] columns = {"菜品名称", "销售数量", "销售金额", "占比"};
        dishSalesTableModel = new DefaultTableModel(columns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        
        dishSalesTable = new JTable(dishSalesTableModel);
        dishSalesTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        dishSalesTable.getTableHeader().setReorderingAllowed(false);
        
        JScrollPane scrollPane = new JScrollPane(dishSalesTable);
        panel.add(scrollPane, BorderLayout.CENTER);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        JButton exportButton = new JButton("导出报告");
        exportButton.addActionListener(new ExportActionListener());
        
        JButton printButton = new JButton("打印报告");
        printButton.addActionListener(new PrintActionListener());
        
        JButton closeButton = new JButton("关闭");
        closeButton.addActionListener(e -> dispose());
        
        panel.add(exportButton);
        panel.add(printButton);
        panel.add(closeButton);
        
        return panel;
    }
    
    /**
     * 生成今日统计
     */
    private void generateTodayStatistics() {
        LocalDate today = LocalDate.now();
        startDateField.setText(today.toString());
        endDateField.setText(today.toString());
        generateStatistics(today, today);
    }
    
    /**
     * 生成统计报告
     */
    private void generateStatistics(LocalDate startDate, LocalDate endDate) {
        try {
            // 生成总体统计
            StringBuilder summary = new StringBuilder();
            summary.append("=== 销售统计报告 ===\n");
            summary.append("统计期间: ").append(startDate).append(" 至 ").append(endDate).append("\n\n");
            
            // 如果是单日统计，使用现有方法
            if (startDate.equals(endDate)) {
                String report = statisticsService.generateSalesReport(startDate);
                summary.append(report);
                
                // 更新菜品销售表格
                updateDishSalesTable(startDate);
            } else {
                // 多日统计（简化版）
                summary.append("多日统计功能待完善\n");
                summary.append("请选择单日进行详细统计\n");
            }
            
            summaryArea.setText(summary.toString());
            
        } catch (Exception e) {
            JOptionPane.showMessageDialog(this, 
                "生成统计报告失败: " + e.getMessage(), 
                "错误", JOptionPane.ERROR_MESSAGE);
        }
    }
    
    /**
     * 更新菜品销售表格
     */
    private void updateDishSalesTable(LocalDate date) {
        dishSalesTableModel.setRowCount(0);
        
        SalesStatisticsService.SalesStatistics statistics = statisticsService.getSalesStatistics(date);
        List<SalesStatisticsService.DishSalesInfo> dishSales = 
            statistics.getDishSales().values().stream()
                .sorted((a, b) -> b.getQuantity() - a.getQuantity())
                .toList();
        
        int totalQuantity = dishSales.stream().mapToInt(SalesStatisticsService.DishSalesInfo::getQuantity).sum();
        
        for (SalesStatisticsService.DishSalesInfo dishInfo : dishSales) {
            double percentage = totalQuantity > 0 ? (double) dishInfo.getQuantity() / totalQuantity * 100 : 0;
            
            Object[] row = {
                dishInfo.getDishName(),
                dishInfo.getQuantity() + "份",
                "¥" + dishInfo.getTotalAmount(),
                String.format("%.1f%%", percentage)
            };
            dishSalesTableModel.addRow(row);
        }
    }
    
    /**
     * 查询按钮事件监听器
     */
    private class QueryActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                LocalDate startDate = LocalDate.parse(startDateField.getText());
                LocalDate endDate = LocalDate.parse(endDateField.getText());
                
                if (startDate.isAfter(endDate)) {
                    JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                        "开始日期不能晚于结束日期", "日期错误", JOptionPane.ERROR_MESSAGE);
                    return;
                }
                
                generateStatistics(startDate, endDate);
                
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                    "日期格式错误，请使用 yyyy-MM-dd 格式", 
                    "格式错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 导出按钮事件监听器
     */
    private class ExportActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setSelectedFile(new java.io.File("销售统计报告_" + LocalDate.now() + ".txt"));
            
            if (fileChooser.showSaveDialog(SalesStatisticsDialog.this) == JFileChooser.APPROVE_OPTION) {
                try {
                    java.io.FileWriter writer = new java.io.FileWriter(fileChooser.getSelectedFile());
                    writer.write(summaryArea.getText());
                    writer.close();
                    
                    JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                        "报告导出成功", "导出", JOptionPane.INFORMATION_MESSAGE);
                } catch (Exception ex) {
                    JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                        "导出失败: " + ex.getMessage(), "导出错误", JOptionPane.ERROR_MESSAGE);
                }
            }
        }
    }
    
    /**
     * 打印按钮事件监听器
     */
    private class PrintActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            try {
                boolean doPrint = summaryArea.print();
                if (doPrint) {
                    JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                        "报告打印成功", "打印", JOptionPane.INFORMATION_MESSAGE);
                }
            } catch (Exception ex) {
                JOptionPane.showMessageDialog(SalesStatisticsDialog.this, 
                    "打印失败: " + ex.getMessage(), "打印错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
}
