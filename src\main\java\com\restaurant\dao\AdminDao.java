package com.restaurant.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.restaurant.model.Admin;
import com.restaurant.util.FileUtil;
import com.restaurant.util.JsonUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 管理员数据访问对象
 */
public class AdminDao {
    
    private List<Admin> admins;
    
    public AdminDao() {
        this.admins = new ArrayList<>();
        loadAdmins();
    }
    
    /**
     * 从文件加载管理员数据
     */
    private void loadAdmins() {
        try {
            if (FileUtil.fileExists(FileUtil.ADMIN_FILE)) {
                List<Admin> loadedAdmins = JsonUtil.readFromFile(FileUtil.ADMIN_FILE, new TypeReference<List<Admin>>() {});
                if (loadedAdmins != null) {
                    this.admins = loadedAdmins;
                }
            } else {
                // 如果文件不存在，创建默认管理员
                createDefaultAdmin();
                saveAdmins();
            }
        } catch (IOException e) {
            System.err.println("加载管理员数据失败: " + e.getMessage());
            createDefaultAdmin();
        }
    }
    
    /**
     * 创建默认管理员
     */
    private void createDefaultAdmin() {
        admins.clear();
        // 创建默认管理员账户
        Admin defaultAdmin = new Admin("admin", "123456", "系统管理员");
        defaultAdmin.setEmail("<EMAIL>");
        admins.add(defaultAdmin);
    }
    
    /**
     * 保存管理员数据到文件
     */
    public void saveAdmins() {
        try {
            FileUtil.initializeDataDirectories();
            JsonUtil.writeToFile(admins, FileUtil.ADMIN_FILE);
        } catch (IOException e) {
            System.err.println("保存管理员数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据用户名查找管理员
     * @param username 用户名
     * @return 管理员（可能为空）
     */
    public Optional<Admin> findByUsername(String username) {
        return admins.stream()
                .filter(admin -> admin.getUsername().equals(username))
                .findFirst();
    }
    
    /**
     * 验证管理员登录
     * @param username 用户名
     * @param password 密码
     * @return 管理员（可能为空）
     */
    public Optional<Admin> validateLogin(String username, String password) {
        return admins.stream()
                .filter(admin -> admin.getUsername().equals(username) && 
                               admin.validatePassword(password) && 
                               admin.isActive())
                .findFirst();
    }
    
    /**
     * 添加管理员
     * @param admin 管理员
     * @return 是否添加成功
     */
    public boolean addAdmin(Admin admin) {
        if (admin == null || findByUsername(admin.getUsername()).isPresent()) {
            return false;
        }
        admins.add(admin);
        saveAdmins();
        return true;
    }
    
    /**
     * 更新管理员
     * @param admin 管理员
     * @return 是否更新成功
     */
    public boolean updateAdmin(Admin admin) {
        if (admin == null) {
            return false;
        }
        
        for (int i = 0; i < admins.size(); i++) {
            if (admins.get(i).getUsername().equals(admin.getUsername())) {
                admins.set(i, admin);
                saveAdmins();
                return true;
            }
        }
        return false;
    }
    
    /**
     * 删除管理员
     * @param username 用户名
     * @return 是否删除成功
     */
    public boolean deleteAdmin(String username) {
        boolean removed = admins.removeIf(admin -> admin.getUsername().equals(username));
        if (removed) {
            saveAdmins();
        }
        return removed;
    }
    
    /**
     * 获取所有管理员
     * @return 管理员列表
     */
    public List<Admin> getAllAdmins() {
        return new ArrayList<>(admins);
    }
    
    /**
     * 更新管理员最后登录时间
     * @param username 用户名
     */
    public void updateLastLoginTime(String username) {
        Optional<Admin> adminOpt = findByUsername(username);
        if (adminOpt.isPresent()) {
            Admin admin = adminOpt.get();
            admin.updateLastLoginTime();
            updateAdmin(admin);
        }
    }
}
