@echo off
echo 正在启动餐厅自助点餐系统...
echo.

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保已安装Java 17或更高版本
    pause
    exit /b 1
)

REM 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告：未找到Maven，尝试直接运行编译后的类文件...
    if exist "target\classes\com\restaurant\RestaurantApplication.class" (
        echo 找到编译后的类文件，正在启动...
        java -cp "target\classes;target\dependency\*" com.restaurant.RestaurantApplication
    ) else (
        echo 错误：未找到编译后的类文件，请先编译项目
        echo 请安装Maven并运行: mvn compile
        pause
        exit /b 1
    )
) else (
    echo 正在编译项目...
    mvn compile
    if %errorlevel% neq 0 (
        echo 编译失败，请检查代码
        pause
        exit /b 1
    )
    
    echo 编译成功，正在启动应用程序...
    mvn exec:java -Dexec.mainClass="com.restaurant.RestaurantApplication"
)

pause
