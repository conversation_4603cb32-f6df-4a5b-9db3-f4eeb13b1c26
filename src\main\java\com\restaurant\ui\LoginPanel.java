package com.restaurant.ui;

import com.restaurant.service.AdminService;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyAdapter;
import java.awt.event.KeyEvent;

/**
 * 登录面板
 * Login Panel
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class LoginPanel extends JPanel {
    
    private final MainFrame mainFrame;
    private final AdminService adminService;
    
    private JTextField usernameField;
    private JPasswordField passwordField;
    private JButton loginButton;
    private JButton backButton;
    
    public LoginPanel(MainFrame mainFrame) {
        this.mainFrame = mainFrame;
        this.adminService = mainFrame.getAdminService();
        initializeComponents();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        setBackground(new Color(245, 245, 245));
        
        // 创建主面板
        JPanel centerPanel = new JPanel(new GridBagLayout());
        centerPanel.setOpaque(false);
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(10, 10, 10, 10);
        
        // 标题
        JLabel titleLabel = new JLabel("管理员登录");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 24));
        titleLabel.setForeground(new Color(51, 51, 51));
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        centerPanel.add(titleLabel, gbc);
        
        // 用户名标签
        JLabel usernameLabel = new JLabel("用户名:");
        usernameLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 1;
        gbc.anchor = GridBagConstraints.EAST;
        centerPanel.add(usernameLabel, gbc);
        
        // 用户名输入框
        usernameField = new JTextField(20);
        usernameField.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        usernameField.setPreferredSize(new Dimension(200, 30));
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        centerPanel.add(usernameField, gbc);
        
        // 密码标签
        JLabel passwordLabel = new JLabel("密码:");
        passwordLabel.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        gbc.gridx = 0;
        gbc.gridy = 2;
        gbc.anchor = GridBagConstraints.EAST;
        centerPanel.add(passwordLabel, gbc);
        
        // 密码输入框
        passwordField = new JPasswordField(20);
        passwordField.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        passwordField.setPreferredSize(new Dimension(200, 30));
        gbc.gridx = 1;
        gbc.anchor = GridBagConstraints.WEST;
        centerPanel.add(passwordField, gbc);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new FlowLayout());
        buttonPanel.setOpaque(false);
        
        // 登录按钮
        loginButton = new JButton("登录");
        loginButton.setFont(new Font("微软雅黑", Font.BOLD, 14));
        loginButton.setPreferredSize(new Dimension(80, 35));
        loginButton.setBackground(new Color(52, 152, 219));
        loginButton.setForeground(Color.WHITE);
        loginButton.setFocusPainted(false);
        loginButton.setBorderPainted(false);
        loginButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        loginButton.addActionListener(new LoginActionListener());
        
        // 返回按钮
        backButton = new JButton("返回");
        backButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        backButton.setPreferredSize(new Dimension(80, 35));
        backButton.setBackground(new Color(149, 165, 166));
        backButton.setForeground(Color.WHITE);
        backButton.setFocusPainted(false);
        backButton.setBorderPainted(false);
        backButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        backButton.addActionListener(e -> mainFrame.showWelcomePanel());
        
        buttonPanel.add(loginButton);
        buttonPanel.add(backButton);
        
        gbc.gridx = 0;
        gbc.gridy = 3;
        gbc.gridwidth = 2;
        gbc.anchor = GridBagConstraints.CENTER;
        centerPanel.add(buttonPanel, gbc);
        
        // 提示信息
        JLabel hintLabel = new JLabel("<html><center>默认账户：admin<br>默认密码：123456</center></html>");
        hintLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        hintLabel.setForeground(new Color(149, 165, 166));
        gbc.gridy = 4;
        gbc.insets = new Insets(20, 10, 10, 10);
        centerPanel.add(hintLabel, gbc);
        
        add(centerPanel, BorderLayout.CENTER);
        
        // 添加键盘事件监听
        addKeyListeners();
    }
    
    /**
     * 添加键盘事件监听
     */
    private void addKeyListeners() {
        KeyAdapter enterKeyListener = new KeyAdapter() {
            @Override
            public void keyPressed(KeyEvent e) {
                if (e.getKeyCode() == KeyEvent.VK_ENTER) {
                    performLogin();
                }
            }
        };
        
        usernameField.addKeyListener(enterKeyListener);
        passwordField.addKeyListener(enterKeyListener);
    }
    
    /**
     * 执行登录
     */
    private void performLogin() {
        String username = usernameField.getText().trim();
        String password = new String(passwordField.getPassword());
        
        if (username.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入用户名", "提示", JOptionPane.WARNING_MESSAGE);
            usernameField.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            JOptionPane.showMessageDialog(this, "请输入密码", "提示", JOptionPane.WARNING_MESSAGE);
            passwordField.requestFocus();
            return;
        }
        
        // 显示登录进度
        loginButton.setEnabled(false);
        loginButton.setText("登录中...");
        
        // 使用SwingWorker在后台执行登录
        SwingWorker<Boolean, Void> loginWorker = new SwingWorker<Boolean, Void>() {
            @Override
            protected Boolean doInBackground() throws Exception {
                return adminService.login(username, password);
            }
            
            @Override
            protected void done() {
                try {
                    boolean success = get();
                    if (success) {
                        JOptionPane.showMessageDialog(LoginPanel.this, 
                            "登录成功！欢迎 " + adminService.getCurrentAdmin().getName(), 
                            "登录成功", JOptionPane.INFORMATION_MESSAGE);
                        clearFields();
                        mainFrame.showAdminPanel();
                    } else {
                        JOptionPane.showMessageDialog(LoginPanel.this, 
                            "用户名或密码错误", "登录失败", JOptionPane.ERROR_MESSAGE);
                        passwordField.selectAll();
                        passwordField.requestFocus();
                    }
                } catch (Exception e) {
                    JOptionPane.showMessageDialog(LoginPanel.this, 
                        "登录时发生错误: " + e.getMessage(), "错误", JOptionPane.ERROR_MESSAGE);
                } finally {
                    loginButton.setEnabled(true);
                    loginButton.setText("登录");
                }
            }
        };
        
        loginWorker.execute();
    }
    
    /**
     * 清空输入框
     */
    private void clearFields() {
        usernameField.setText("");
        passwordField.setText("");
    }
    
    /**
     * 登录按钮事件监听器
     */
    private class LoginActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            performLogin();
        }
    }
    
    /**
     * 面板显示时的处理
     */
    public void onPanelShown() {
        clearFields();
        usernameField.requestFocus();
    }
}
