package com.restaurant.dao.mysql;

import com.restaurant.model.Admin;
import com.restaurant.util.DatabaseConfig;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * MySQL管理员数据访问对象
 */
public class MySQLAdminDao {
    
    private final DatabaseConfig dbConfig;
    
    public MySQLAdminDao() {
        this.dbConfig = DatabaseConfig.getInstance();
    }
    
    /**
     * 根据用户名查找管理员
     */
    public Optional<Admin> findByUsername(String username) {
        String sql = "SELECT * FROM admin WHERE username = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToAdmin(rs));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("根据用户名查找管理员失败: " + e.getMessage());
        }
        
        return Optional.empty();
    }
    
    /**
     * 验证管理员登录
     */
    public Optional<Admin> validateLogin(String username, String password) {
        String sql = "SELECT * FROM admin WHERE username = ? AND password = ? AND active = 1";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            stmt.setString(2, password);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return Optional.of(mapResultSetToAdmin(rs));
                }
            }
            
        } catch (SQLException e) {
            System.err.println("验证管理员登录失败: " + e.getMessage());
        }
        
        return Optional.empty();
    }
    
    /**
     * 添加管理员
     */
    public boolean addAdmin(Admin admin) {
        String sql = "INSERT INTO admin (username, password, name, email, active) VALUES (?, ?, ?, ?, ?)";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, admin.getUsername());
            stmt.setString(2, admin.getPassword());
            stmt.setString(3, admin.getName());
            stmt.setString(4, admin.getEmail());
            stmt.setBoolean(5, admin.isActive());
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("添加管理员失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 更新管理员
     */
    public boolean updateAdmin(Admin admin) {
        String sql = "UPDATE admin SET password = ?, name = ?, email = ?, active = ?, updated_time = CURRENT_TIMESTAMP WHERE username = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, admin.getPassword());
            stmt.setString(2, admin.getName());
            stmt.setString(3, admin.getEmail());
            stmt.setBoolean(4, admin.isActive());
            stmt.setString(5, admin.getUsername());
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("更新管理员失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 删除管理员
     */
    public boolean deleteAdmin(String username) {
        String sql = "DELETE FROM admin WHERE username = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            
            int rowsAffected = stmt.executeUpdate();
            return rowsAffected > 0;
            
        } catch (SQLException e) {
            System.err.println("删除管理员失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取所有管理员
     */
    public List<Admin> getAllAdmins() {
        List<Admin> admins = new ArrayList<>();
        String sql = "SELECT * FROM admin ORDER BY username";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                admins.add(mapResultSetToAdmin(rs));
            }
            
        } catch (SQLException e) {
            System.err.println("获取所有管理员失败: " + e.getMessage());
        }
        
        return admins;
    }
    
    /**
     * 更新管理员最后登录时间
     */
    public void updateLastLoginTime(String username) {
        String sql = "UPDATE admin SET last_login_time = CURRENT_TIMESTAMP WHERE username = ?";
        
        try (Connection conn = dbConfig.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, username);
            stmt.executeUpdate();
            
        } catch (SQLException e) {
            System.err.println("更新最后登录时间失败: " + e.getMessage());
        }
    }
    
    /**
     * 将ResultSet映射为Admin对象
     */
    private Admin mapResultSetToAdmin(ResultSet rs) throws SQLException {
        Admin admin = new Admin();
        admin.setUsername(rs.getString("username"));
        admin.setPassword(rs.getString("password"));
        admin.setName(rs.getString("name"));
        admin.setEmail(rs.getString("email"));
        admin.setActive(rs.getBoolean("active"));
        
        Timestamp lastLoginTime = rs.getTimestamp("last_login_time");
        if (lastLoginTime != null) {
            admin.setLastLoginTime(lastLoginTime.toLocalDateTime());
        }
        
        return admin;
    }
}
