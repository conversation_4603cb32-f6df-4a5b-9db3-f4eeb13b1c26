package com.restaurant.util;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.sql.Connection;
import java.sql.Statement;

/**
 * 数据库初始化工具类
 * Database Initializer Utility
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DatabaseInitializer {
    
    private final DatabaseConfig dbConfig;
    
    public DatabaseInitializer() {
        this.dbConfig = DatabaseConfig.getInstance();
    }
    
    /**
     * 初始化数据库
     */
    public boolean initializeDatabase() {
        try {
            System.out.println("开始初始化数据库...");
            
            // 测试数据库连接
            if (!dbConfig.testConnection()) {
                System.err.println("数据库连接失败，请检查配置");
                return false;
            }
            
            // 执行初始化脚本
            if (executeInitScript()) {
                System.out.println("数据库初始化成功");
                return true;
            } else {
                System.err.println("数据库初始化失败");
                return false;
            }
            
        } catch (Exception e) {
            System.err.println("数据库初始化过程中发生错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 执行初始化脚本
     */
    private boolean executeInitScript() {
        try (Connection conn = dbConfig.getConnection();
             InputStream inputStream = getClass().getClassLoader().getResourceAsStream("sql/init_mysql.sql")) {
            
            if (inputStream == null) {
                System.err.println("未找到数据库初始化脚本文件");
                return false;
            }
            
            // 读取SQL脚本
            StringBuilder sqlScript = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    sqlScript.append(line).append("\n");
                }
            }
            
            // 分割并执行SQL语句
            String[] sqlStatements = sqlScript.toString().split(";");
            
            try (Statement stmt = conn.createStatement()) {
                for (String sql : sqlStatements) {
                    sql = sql.trim();
                    // 跳过空行和注释
                    if (!sql.isEmpty() && !sql.startsWith("--") && !sql.startsWith("/*")) {
                        try {
                            stmt.execute(sql);
                            System.out.println("执行SQL: " + sql.substring(0, Math.min(50, sql.length())) + "...");
                        } catch (Exception e) {
                            // 某些语句可能因为表已存在等原因失败，这是正常的
                            System.out.println("SQL执行警告: " + e.getMessage());
                        }
                    }
                }
            }
            
            return true;
            
        } catch (Exception e) {
            System.err.println("执行初始化脚本失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 检查数据库是否已初始化
     */
    public boolean isDatabaseInitialized() {
        try (Connection conn = dbConfig.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 检查关键表是否存在
            stmt.executeQuery("SELECT COUNT(*) FROM admin LIMIT 1");
            stmt.executeQuery("SELECT COUNT(*) FROM dish LIMIT 1");
            stmt.executeQuery("SELECT COUNT(*) FROM orders LIMIT 1");
            stmt.executeQuery("SELECT COUNT(*) FROM order_item LIMIT 1");
            
            return true;
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 创建数据库（如果不存在）
     */
    public boolean createDatabaseIfNotExists() {
        try {
            String host = dbConfig.getProperty("mysql.host", "localhost");
            String port = dbConfig.getProperty("mysql.port", "3306");
            String database = dbConfig.getProperty("mysql.database", "restaurant_db");
            String username = dbConfig.getProperty("mysql.username", "root");
            String password = dbConfig.getProperty("mysql.password", "");
            
            // 连接到MySQL服务器（不指定数据库）
            String jdbcUrl = String.format("*******************************************************************************************************", 
                    host, port);
            
            try (Connection conn = java.sql.DriverManager.getConnection(jdbcUrl, username, password);
                 Statement stmt = conn.createStatement()) {
                
                // 创建数据库
                String createDbSql = String.format("CREATE DATABASE IF NOT EXISTS %s DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", database);
                stmt.execute(createDbSql);
                
                System.out.println("数据库 " + database + " 创建成功或已存在");
                return true;
                
            }
            
        } catch (Exception e) {
            System.err.println("创建数据库失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 完整的数据库设置流程
     */
    public boolean setupDatabase() {
        System.out.println("=== 开始数据库设置 ===");
        
        // 1. 创建数据库
        if (!createDatabaseIfNotExists()) {
            System.err.println("创建数据库失败");
            return false;
        }
        
        // 2. 检查是否已初始化
        if (isDatabaseInitialized()) {
            System.out.println("数据库已初始化，跳过初始化步骤");
            return true;
        }
        
        // 3. 初始化数据库
        if (!initializeDatabase()) {
            System.err.println("初始化数据库失败");
            return false;
        }
        
        System.out.println("=== 数据库设置完成 ===");
        return true;
    }
}
