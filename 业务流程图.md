# 餐厅自助点餐系统 - 业务流程图

## 概述

本文档描述了餐厅自助点餐系统的完整业务流程，包括顾客点餐流程和管理员管理流程。

## 业务流程图

```mermaid
flowchart TD
    Start([系统启动]) --> Welcome[欢迎界面]
    
    Welcome --> UserChoice{用户选择}
    UserChoice -->|顾客点餐| CustomerFlow[顾客点餐流程]
    UserChoice -->|管理员登录| AdminLogin[管理员登录]
    
    %% 顾客点餐流程
    CustomerFlow --> SelectDish[浏览选择菜品]
    SelectDish --> FilterDish{筛选菜品}
    FilterDish -->|按分类| CategoryFilter[分类筛选]
    FilterDish -->|搜索| SearchFilter[关键词搜索]
    CategoryFilter --> AddToCart[添加到购物车]
    SearchFilter --> AddToCart
    
    AddToCart --> CartCheck{购物车检查}
    CartCheck -->|继续添加| SelectDish
    CartCheck -->|确认订单| OrderType{选择订单类型}
    
    OrderType -->|在店消费| DineIn[填写餐桌信息]
    OrderType -->|外卖订单| Takeaway[填写配送信息]
    
    DineIn --> TableInfo[餐桌号/包厢费]
    Takeaway --> DeliveryInfo[配送地址/联系电话]
    
    TableInfo --> OrderConfirm[订单确认]
    DeliveryInfo --> OrderConfirm
    
    OrderConfirm --> Payment[提交订单]
    Payment --> OrderSuccess[订单成功]
    OrderSuccess --> PrintReceipt[打印小票]
    PrintReceipt --> End1([流程结束])
    
    %% 管理员流程
    AdminLogin --> LoginCheck{登录验证}
    LoginCheck -->|登录失败| LoginFail[登录失败]
    LoginFail --> AdminLogin
    LoginCheck -->|登录成功| AdminPanel[管理员控制台]
    
    AdminPanel --> AdminChoice{管理功能选择}
    
    %% 菜品管理
    AdminChoice -->|菜品管理| DishMgmt[菜品管理]
    DishMgmt --> DishOp{菜品操作}
    DishOp -->|添加菜品| AddDish[添加新菜品]
    DishOp -->|编辑菜品| EditDish[编辑菜品信息]
    DishOp -->|删除菜品| DeleteDish[删除菜品]
    DishOp -->|设置折扣| SetDiscount[设置菜品折扣]
    
    AddDish --> DishSave[保存菜品]
    EditDish --> DishSave
    DeleteDish --> DishSave
    SetDiscount --> DishSave
    DishSave --> AdminPanel
    
    %% 订单管理
    AdminChoice -->|订单管理| OrderMgmt[订单管理]
    OrderMgmt --> OrderOp{订单操作}
    OrderOp -->|查看订单| ViewOrder[查看订单详情]
    OrderOp -->|删除订单| DeleteOrder[删除订单]
    OrderOp -->|打印订单| PrintOrder[打印订单]
    
    ViewOrder --> AdminPanel
    DeleteOrder --> AdminPanel
    PrintOrder --> AdminPanel
    
    %% 销售统计
    AdminChoice -->|销售统计| SalesStats[销售统计]
    SalesStats --> StatsOp{统计操作}
    StatsOp -->|日报表| DailyReport[生成日报表]
    StatsOp -->|高级统计| AdvancedStats[高级统计分析]
    StatsOp -->|导出报告| ExportReport[导出统计报告]
    
    DailyReport --> AdminPanel
    AdvancedStats --> AdminPanel
    ExportReport --> AdminPanel
    
    %% 系统管理
    AdminChoice -->|系统管理| SystemMgmt[系统管理]
    SystemMgmt --> SysOp{系统操作}
    SysOp -->|系统设置| SystemConfig[系统参数配置]
    SysOp -->|数据备份| DataBackup[数据备份]
    SysOp -->|数据恢复| DataRestore[数据恢复]
    SysOp -->|个人信息| ProfileMgmt[个人信息管理]
    
    SystemConfig --> AdminPanel
    DataBackup --> AdminPanel
    DataRestore --> AdminPanel
    ProfileMgmt --> AdminPanel
    
    %% 退出流程
    AdminChoice -->|退出登录| Logout[退出登录]
    Logout --> Welcome
    
    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef admin fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef customer fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    
    class Start,End1,PrintReceipt startEnd
    class Welcome,AdminPanel,OrderSuccess process
    class UserChoice,FilterDish,CartCheck,OrderType,LoginCheck,AdminChoice,DishOp,OrderOp,StatsOp,SysOp decision
    class AdminLogin,DishMgmt,OrderMgmt,SalesStats,SystemMgmt,AddDish,EditDish,DeleteDish,SetDiscount,ViewOrder,DeleteOrder,PrintOrder,DailyReport,AdvancedStats,ExportReport,SystemConfig,DataBackup,DataRestore,ProfileMgmt admin
    class CustomerFlow,SelectDish,AddToCart,DineIn,Takeaway,TableInfo,DeliveryInfo,OrderConfirm,Payment customer
```

## 流程说明

### 1. 系统入口
- **系统启动**: 应用程序启动后显示欢迎界面
- **用户选择**: 用户可以选择"顾客点餐"或"管理员登录"

### 2. 顾客点餐流程
#### 2.1 菜品选择
- **浏览菜品**: 顾客可以浏览所有可用菜品
- **筛选功能**: 
  - 按分类筛选（主食、汤类、素食、饮品等）
  - 关键词搜索菜品名称或描述
- **添加到购物车**: 选择数量并添加菜品到订单

#### 2.2 订单确认
- **购物车检查**: 确认已选菜品，可继续添加或确认订单
- **订单类型选择**: 
  - **在店消费**: 填写餐桌号，可选择包厢服务
  - **外卖订单**: 填写配送地址和联系电话

#### 2.3 订单提交
- **订单确认**: 核对订单信息和总金额
- **提交订单**: 保存订单到系统
- **订单成功**: 显示订单详情和订单号
- **打印小票**: 可选择打印订单小票

### 3. 管理员管理流程
#### 3.1 登录验证
- **管理员登录**: 输入用户名和密码
- **登录验证**: 验证管理员身份
- **登录失败**: 重新输入登录信息
- **登录成功**: 进入管理员控制台

#### 3.2 菜品管理
- **添加菜品**: 创建新的菜品信息
- **编辑菜品**: 修改现有菜品的信息
- **删除菜品**: 移除不再提供的菜品
- **设置折扣**: 为菜品设置促销折扣

#### 3.3 订单管理
- **查看订单**: 查看订单详细信息
- **删除订单**: 删除错误或取消的订单
- **打印订单**: 打印订单详情

#### 3.4 销售统计
- **日报表**: 生成当日销售统计报告
- **高级统计**: 进行多维度数据分析
- **导出报告**: 导出统计数据到文件

#### 3.5 系统管理
- **系统设置**: 配置系统参数
- **数据备份**: 备份重要业务数据
- **数据恢复**: 从备份恢复数据
- **个人信息**: 管理管理员个人信息

### 4. 流程特点
- **用户友好**: 界面简洁，操作流程清晰
- **功能完整**: 覆盖点餐和管理的全部业务场景
- **数据安全**: 管理员权限控制，数据备份恢复
- **灵活配置**: 支持在店消费和外卖两种模式

## 技术实现
- **前端**: Java Swing GUI界面
- **后端**: Java业务逻辑处理
- **数据存储**: 支持JSON文件和MySQL数据库
- **架构模式**: MVC分层架构，DAO数据访问模式
