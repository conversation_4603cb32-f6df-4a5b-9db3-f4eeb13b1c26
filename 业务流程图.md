# 餐厅自助点餐系统 - 业务流程图

## 概述

本文档描述了餐厅自助点餐系统的业务流程，分为顾客端和管理端两个独立的流程图，便于不同角色的用户理解和使用。

## 1. 顾客端业务流程图

```mermaid
flowchart TD
    Start([顾客进入系统]) --> Welcome[欢迎界面]
    Welcome --> CustomerStart[开始点餐]

    %% 菜品浏览和选择
    CustomerStart --> BrowseDish[浏览菜品列表]
    BrowseDish --> FilterChoice{需要筛选吗?}

    FilterChoice -->|是| FilterType{筛选方式}
    FilterChoice -->|否| SelectDish[选择菜品]

    FilterType -->|按分类| CategoryFilter[选择菜品分类]
    FilterType -->|关键词搜索| SearchDish[输入搜索关键词]

    CategoryFilter --> FilteredList[显示筛选结果]
    SearchDish --> FilteredList
    FilteredList --> SelectDish

    %% 添加到订单
    SelectDish --> SetQuantity[设置菜品数量]
    SetQuantity --> AddToOrder[添加到订单]
    AddToOrder --> ContinueAdd{继续添加菜品?}

    ContinueAdd -->|是| BrowseDish
    ContinueAdd -->|否| ReviewOrder[查看订单详情]

    %% 订单类型选择
    ReviewOrder --> OrderTypeChoice{选择订单类型}

    OrderTypeChoice -->|在店消费| DineInInfo[填写在店消费信息]
    OrderTypeChoice -->|外卖订单| TakeawayInfo[填写外卖信息]

    %% 在店消费信息
    DineInInfo --> TableNumber[输入餐桌号]
    TableNumber --> PrivateRoom{需要包厢?}
    PrivateRoom -->|是| PrivateRoomFee[设置包厢费用]
    PrivateRoom -->|否| DineInConfirm[确认在店消费信息]
    PrivateRoomFee --> DineInConfirm

    %% 外卖信息
    TakeawayInfo --> DeliveryAddress[输入配送地址]
    DeliveryAddress --> CustomerPhone[输入联系电话]
    CustomerPhone --> DeliveryFee[确认配送费用]
    DeliveryFee --> TakeawayConfirm[确认外卖信息]

    %% 订单确认和提交
    DineInConfirm --> FinalReview[最终订单确认]
    TakeawayConfirm --> FinalReview

    FinalReview --> TotalAmount[显示订单总金额]
    TotalAmount --> SubmitOrder[提交订单]

    SubmitOrder --> OrderResult{订单提交结果}
    OrderResult -->|成功| OrderSuccess[订单提交成功]
    OrderResult -->|失败| OrderFailed[订单提交失败]

    OrderSuccess --> ShowOrderDetails[显示订单详情]
    ShowOrderDetails --> PrintOption{需要打印小票?}
    PrintOption -->|是| PrintReceipt[打印订单小票]
    PrintOption -->|否| CustomerEnd([完成点餐])
    PrintReceipt --> CustomerEnd

    OrderFailed --> RetrySubmit{重新提交?}
    RetrySubmit -->|是| SubmitOrder
    RetrySubmit -->|否| BackToReview[返回订单确认]
    BackToReview --> FinalReview

    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px

    class Start,CustomerEnd startEnd
    class Welcome,CustomerStart,BrowseDish,SelectDish,SetQuantity,AddToOrder,ReviewOrder,DineInInfo,TakeawayInfo,TableNumber,PrivateRoomFee,DeliveryAddress,CustomerPhone,DeliveryFee,FinalReview,TotalAmount,SubmitOrder,ShowOrderDetails,PrintReceipt,FilteredList process
    class FilterChoice,FilterType,ContinueAdd,OrderTypeChoice,PrivateRoom,PrintOption,OrderResult,RetrySubmit decision
    class OrderSuccess success
    class OrderFailed error
```

## 2. 管理端业务流程图

```mermaid
flowchart TD
    AdminStart([管理员进入系统]) --> AdminLogin[管理员登录界面]

    %% 登录流程
    AdminLogin --> InputCredentials[输入用户名密码]
    InputCredentials --> LoginValidation{登录验证}

    LoginValidation -->|验证失败| LoginError[显示登录错误]
    LoginError --> InputCredentials
    LoginValidation -->|验证成功| AdminDashboard[管理员控制台]

    %% 主功能选择
    AdminDashboard --> FunctionChoice{选择管理功能}

    %% 菜品管理流程
    FunctionChoice -->|菜品管理| DishManagement[菜品管理模块]
    DishManagement --> DishOperation{菜品操作选择}

    DishOperation -->|添加菜品| AddDish[添加新菜品]
    DishOperation -->|编辑菜品| EditDish[编辑菜品信息]
    DishOperation -->|删除菜品| DeleteDish[删除菜品]
    DishOperation -->|设置折扣| SetDiscount[设置菜品折扣]
    DishOperation -->|查看菜品| ViewDish[查看菜品列表]

    AddDish --> DishForm[填写菜品信息表单]
    EditDish --> SelectDishToEdit[选择要编辑的菜品]
    SelectDishToEdit --> DishForm
    DeleteDish --> ConfirmDelete{确认删除菜品?}
    SetDiscount --> SelectDishForDiscount[选择要设置折扣的菜品]
    SelectDishForDiscount --> DiscountForm[设置折扣信息]

    DishForm --> SaveDish[保存菜品信息]
    DiscountForm --> SaveDish
    ConfirmDelete -->|是| DeleteConfirmed[执行删除操作]
    ConfirmDelete -->|否| DishOperation

    SaveDish --> DishSaveResult{保存结果}
    DeleteConfirmed --> DishSaveResult
    DishSaveResult -->|成功| DishSuccess[操作成功]
    DishSaveResult -->|失败| DishError[操作失败]

    DishSuccess --> DishManagement
    DishError --> DishManagement
    ViewDish --> DishManagement

    %% 订单管理流程
    FunctionChoice -->|订单管理| OrderManagement[订单管理模块]
    OrderManagement --> OrderOperation{订单操作选择}

    OrderOperation -->|查看今日订单| ViewTodayOrders[显示今日订单列表]
    OrderOperation -->|查看订单详情| ViewOrderDetails[选择订单查看详情]
    OrderOperation -->|删除订单| DeleteOrder[选择要删除的订单]
    OrderOperation -->|打印订单| PrintOrder[选择要打印的订单]
    OrderOperation -->|刷新订单| RefreshOrders[刷新订单数据]

    ViewOrderDetails --> OrderDetailDialog[显示订单详情对话框]
    DeleteOrder --> ConfirmOrderDelete{确认删除订单?}
    PrintOrder --> OrderPrintDialog[订单打印对话框]

    ConfirmOrderDelete -->|是| ExecuteOrderDelete[执行删除操作]
    ConfirmOrderDelete -->|否| OrderOperation

    ExecuteOrderDelete --> OrderDeleteResult{删除结果}
    OrderDeleteResult -->|成功| OrderDeleteSuccess[删除成功]
    OrderDeleteResult -->|失败| OrderDeleteError[删除失败]

    ViewTodayOrders --> OrderManagement
    OrderDetailDialog --> OrderManagement
    OrderPrintDialog --> OrderManagement
    OrderDeleteSuccess --> OrderManagement
    OrderDeleteError --> OrderManagement
    RefreshOrders --> OrderManagement

    %% 销售统计流程
    FunctionChoice -->|销售统计| SalesStatistics[销售统计模块]
    SalesStatistics --> StatsOperation{统计操作选择}

    StatsOperation -->|生成日报表| DailyReport[选择日期生成报表]
    StatsOperation -->|高级统计| AdvancedStats[打开高级统计对话框]
    StatsOperation -->|导出报告| ExportReport[选择导出格式]

    DailyReport --> InputDate[输入统计日期]
    InputDate --> GenerateReport[生成统计报告]
    GenerateReport --> DisplayReport[显示报告内容]

    AdvancedStats --> StatsDialog[高级统计分析对话框]
    ExportReport --> ExportDialog[导出设置对话框]

    DisplayReport --> SalesStatistics
    StatsDialog --> SalesStatistics
    ExportDialog --> SalesStatistics

    %% 系统管理流程
    FunctionChoice -->|系统管理| SystemManagement[系统管理模块]
    SystemManagement --> SystemOperation{系统操作选择}

    SystemOperation -->|系统设置| SystemSettings[系统参数配置]
    SystemOperation -->|数据备份| DataBackup[数据备份操作]
    SystemOperation -->|数据恢复| DataRestore[数据恢复操作]
    SystemOperation -->|个人信息| ProfileManagement[个人信息管理]

    SystemSettings --> SettingsDialog[系统设置对话框]
    DataBackup --> BackupDialog[选择备份位置]
    DataRestore --> RestoreDialog[选择恢复文件]
    ProfileManagement --> ProfileDialog[个人信息编辑对话框]

    SettingsDialog --> SystemManagement
    BackupDialog --> SystemManagement
    RestoreDialog --> SystemManagement
    ProfileDialog --> SystemManagement

    %% 退出流程
    FunctionChoice -->|退出登录| LogoutConfirm{确认退出登录?}
    LogoutConfirm -->|是| Logout[执行退出登录]
    LogoutConfirm -->|否| AdminDashboard
    Logout --> AdminEnd([管理员退出系统])

    %% 返回控制台
    DishManagement --> AdminDashboard
    OrderManagement --> AdminDashboard
    SalesStatistics --> AdminDashboard
    SystemManagement --> AdminDashboard

    %% 样式定义
    classDef startEnd fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef process fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef decision fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef success fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef error fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef dialog fill:#f3e5f5,stroke:#4a148c,stroke-width:2px

    class AdminStart,AdminEnd startEnd
    class AdminLogin,InputCredentials,AdminDashboard,DishManagement,OrderManagement,SalesStatistics,SystemManagement,DishForm,SaveDish,ViewTodayOrders,RefreshOrders,DailyReport,InputDate,GenerateReport,DisplayReport,SystemSettings,DataBackup,DataRestore,ProfileManagement,Logout process
    class LoginValidation,FunctionChoice,DishOperation,DishSaveResult,ConfirmDelete,OrderOperation,ConfirmOrderDelete,OrderDeleteResult,StatsOperation,SystemOperation,LogoutConfirm decision
    class DishSuccess,OrderDeleteSuccess success
    class LoginError,DishError,OrderDeleteError error
    class SelectDishToEdit,SelectDishForDiscount,DiscountForm,DeleteConfirmed,ViewOrderDetails,OrderDetailDialog,PrintOrder,OrderPrintDialog,ExecuteOrderDelete,AdvancedStats,StatsDialog,ExportReport,ExportDialog,SettingsDialog,BackupDialog,RestoreDialog,ProfileDialog dialog
```

## 流程说明

### 1. 顾客端流程说明

#### 1.1 菜品浏览和选择
- **进入系统**: 顾客从欢迎界面开始点餐流程
- **浏览菜品**: 查看所有可用菜品列表
- **筛选功能**:
  - 按分类筛选（主食、汤类、素食、饮品等）
  - 关键词搜索菜品名称或描述
- **选择菜品**: 从筛选结果或完整列表中选择菜品
- **设置数量**: 为选中的菜品设置购买数量
- **添加到订单**: 将菜品加入当前订单

#### 1.2 订单构建
- **继续添加**: 可以重复选择多个菜品
- **查看订单**: 确认已选择的所有菜品和数量
- **订单类型选择**:
  - **在店消费**: 填写餐桌号，可选择包厢服务及费用
  - **外卖订单**: 填写配送地址、联系电话和配送费用

#### 1.3 订单确认和提交
- **最终确认**: 核对所有订单信息和总金额
- **提交订单**: 将订单保存到系统数据库
- **结果处理**:
  - 成功：显示订单详情，可选择打印小票
  - 失败：提示错误信息，可重新提交或返回修改

### 2. 管理端流程说明

#### 2.1 登录验证
- **管理员登录**: 输入用户名和密码进行身份验证
- **登录验证**: 系统验证管理员凭据
- **登录失败**: 显示错误信息，重新输入登录信息
- **登录成功**: 进入管理员控制台主界面

#### 2.2 菜品管理
- **菜品操作**:
  - **添加菜品**: 填写菜品信息表单，创建新菜品
  - **编辑菜品**: 选择现有菜品，修改其信息
  - **删除菜品**: 选择菜品并确认删除操作
  - **设置折扣**: 为指定菜品设置促销折扣
  - **查看菜品**: 浏览所有菜品列表
- **数据保存**: 所有操作结果保存到数据库

#### 2.3 订单管理
- **订单操作**:
  - **查看今日订单**: 显示当天所有订单列表
  - **查看订单详情**: 选择特定订单查看完整信息
  - **删除订单**: 选择并确认删除指定订单
  - **打印订单**: 打印选中订单的详细信息
  - **刷新订单**: 更新订单数据显示

#### 2.4 销售统计
- **统计功能**:
  - **生成日报表**: 选择日期生成销售统计报告
  - **高级统计**: 打开高级统计分析对话框
  - **导出报告**: 将统计数据导出为文件

#### 2.5 系统管理
- **系统功能**:
  - **系统设置**: 配置系统运行参数
  - **数据备份**: 备份重要业务数据到指定位置
  - **数据恢复**: 从备份文件恢复数据
  - **个人信息**: 管理当前管理员的个人信息

#### 2.6 退出流程
- **退出登录**: 确认后退出管理员系统，返回登录界面

### 3. 流程特点

#### 3.1 顾客端特点
- **操作简便**: 流程直观，步骤清晰，易于操作
- **灵活筛选**: 支持分类和关键词两种筛选方式
- **订单灵活**: 支持在店消费和外卖两种订单类型
- **错误处理**: 提供订单提交失败的重试机制

#### 3.2 管理端特点
- **权限控制**: 需要登录验证，确保数据安全
- **功能完整**: 涵盖菜品、订单、统计、系统四大管理模块
- **操作确认**: 重要操作（如删除）需要二次确认
- **数据安全**: 提供数据备份和恢复功能

#### 3.3 系统整体特点
- **角色分离**: 顾客端和管理端功能完全分离
- **流程闭环**: 从点餐到管理形成完整的业务闭环
- **用户体验**: 界面友好，操作反馈及时
- **数据一致**: 两端共享同一数据源，保证数据一致性

## 技术实现
- **前端技术**: Java Swing GUI界面框架
- **后端逻辑**: Java面向对象编程，业务逻辑分层处理
- **数据存储**: 支持JSON文件存储和MySQL数据库存储
- **架构模式**: MVC分层架构，DAO数据访问对象模式
- **设计模式**: 单例模式、工厂模式、观察者模式等
- **数据库**: MySQL关系型数据库，支持事务处理
