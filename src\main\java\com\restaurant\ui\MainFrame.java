package com.restaurant.ui;

import com.restaurant.service.AdminService;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;

/**
 * 主界面框架
 * Main Frame
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class MainFrame extends JFrame {
    
    private final AdminService adminService;
    private JPanel mainPanel;
    private CardLayout cardLayout;
    
    // 界面常量
    private static final String WELCOME_PANEL = "WELCOME";
    private static final String LOGIN_PANEL = "LOGIN";
    private static final String CUSTOMER_PANEL = "CUSTOMER";
    private static final String ADMIN_PANEL = "ADMIN";
    
    public MainFrame() {
        this.adminService = new AdminService();
        initializeFrame();
        initializeComponents();
        showWelcomePanel();
    }
    
    /**
     * 初始化窗口
     */
    private void initializeFrame() {
        setTitle("餐厅自助点餐系统");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        setResizable(true);
        
        // 设置窗口图标（如果有的话）
        try {
            setIconImage(Toolkit.getDefaultToolkit().getImage("icon.png"));
        } catch (Exception e) {
            // 忽略图标加载错误
        }
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        cardLayout = new CardLayout();
        mainPanel = new JPanel(cardLayout);
        
        // 创建各个面板
        mainPanel.add(createWelcomePanel(), WELCOME_PANEL);
        mainPanel.add(new LoginPanel(this), LOGIN_PANEL);
        mainPanel.add(new CustomerOrderPanel(this), CUSTOMER_PANEL);
        mainPanel.add(new AdminManagementPanel(this), ADMIN_PANEL);
        
        add(mainPanel);
    }
    
    /**
     * 创建欢迎面板
     */
    private JPanel createWelcomePanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBackground(new Color(245, 245, 245));
        
        // 标题
        JLabel titleLabel = new JLabel("餐厅自助点餐系统", JLabel.CENTER);
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 36));
        titleLabel.setForeground(new Color(51, 51, 51));
        titleLabel.setBorder(BorderFactory.createEmptyBorder(50, 0, 30, 0));
        
        // 副标题
        JLabel subtitleLabel = new JLabel("Restaurant Self-Ordering System", JLabel.CENTER);
        subtitleLabel.setFont(new Font("Arial", Font.ITALIC, 18));
        subtitleLabel.setForeground(new Color(102, 102, 102));
        
        // 标题面板
        JPanel titlePanel = new JPanel(new BorderLayout());
        titlePanel.setOpaque(false);
        titlePanel.add(titleLabel, BorderLayout.CENTER);
        titlePanel.add(subtitleLabel, BorderLayout.SOUTH);
        
        // 按钮面板
        JPanel buttonPanel = new JPanel(new GridBagLayout());
        buttonPanel.setOpaque(false);
        GridBagConstraints gbc = new GridBagConstraints();
        
        // 顾客点餐按钮
        JButton customerButton = createStyledButton("顾客点餐", new Color(52, 152, 219));
        customerButton.addActionListener(e -> showCustomerPanel());
        gbc.gridx = 0;
        gbc.gridy = 0;
        gbc.insets = new Insets(10, 10, 10, 10);
        buttonPanel.add(customerButton, gbc);
        
        // 管理员登录按钮
        JButton adminButton = createStyledButton("管理员登录", new Color(231, 76, 60));
        adminButton.addActionListener(e -> showLoginPanel());
        gbc.gridx = 1;
        buttonPanel.add(adminButton, gbc);
        
        // 退出按钮
        JButton exitButton = createStyledButton("退出系统", new Color(149, 165, 166));
        exitButton.addActionListener(e -> {
            int result = JOptionPane.showConfirmDialog(this, 
                "确定要退出系统吗？", "确认退出", 
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            if (result == JOptionPane.YES_OPTION) {
                System.exit(0);
            }
        });
        gbc.gridx = 0;
        gbc.gridy = 1;
        gbc.gridwidth = 2;
        gbc.insets = new Insets(20, 10, 10, 10);
        buttonPanel.add(exitButton, gbc);
        
        panel.add(titlePanel, BorderLayout.NORTH);
        panel.add(buttonPanel, BorderLayout.CENTER);
        
        // 底部信息
        JLabel footerLabel = new JLabel("© 2024 Restaurant System v1.0", JLabel.CENTER);
        footerLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        footerLabel.setForeground(new Color(149, 165, 166));
        footerLabel.setBorder(BorderFactory.createEmptyBorder(0, 0, 20, 0));
        panel.add(footerLabel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建样式化按钮
     */
    private JButton createStyledButton(String text, Color backgroundColor) {
        JButton button = new JButton(text);
        button.setFont(new Font("微软雅黑", Font.BOLD, 16));
        button.setForeground(Color.WHITE);
        button.setBackground(backgroundColor);
        button.setPreferredSize(new Dimension(150, 50));
        button.setFocusPainted(false);
        button.setBorderPainted(false);
        button.setCursor(new Cursor(Cursor.HAND_CURSOR));
        
        // 添加鼠标悬停效果
        button.addMouseListener(new java.awt.event.MouseAdapter() {
            @Override
            public void mouseEntered(java.awt.event.MouseEvent evt) {
                button.setBackground(backgroundColor.darker());
            }
            
            @Override
            public void mouseExited(java.awt.event.MouseEvent evt) {
                button.setBackground(backgroundColor);
            }
        });
        
        return button;
    }
    
    /**
     * 显示欢迎面板
     */
    public void showWelcomePanel() {
        cardLayout.show(mainPanel, WELCOME_PANEL);
    }
    
    /**
     * 显示登录面板
     */
    public void showLoginPanel() {
        cardLayout.show(mainPanel, LOGIN_PANEL);
    }
    
    /**
     * 显示顾客点餐面板
     */
    public void showCustomerPanel() {
        cardLayout.show(mainPanel, CUSTOMER_PANEL);
    }
    
    /**
     * 显示管理员面板
     */
    public void showAdminPanel() {
        cardLayout.show(mainPanel, ADMIN_PANEL);
    }
    
    /**
     * 获取管理员服务
     */
    public AdminService getAdminService() {
        return adminService;
    }
}
