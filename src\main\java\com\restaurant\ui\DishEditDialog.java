package com.restaurant.ui;

import com.restaurant.model.Dish;
import com.restaurant.service.DishService;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.math.BigDecimal;

/**
 * 菜品编辑对话框
 */
public class DishEditDialog extends JDialog {
    
    private final DishService dishService;
    private final boolean isEditMode;
    private Dish dish;
    private boolean confirmed = false;
    
    // UI组件
    private JTextField idField;
    private JTextField nameField;
    private JTextField priceField;
    private JComboBox<String> categoryComboBox;
    private JTextArea descriptionArea;
    private JCheckBox availableCheckBox;
    private JTextField discountField;
    
    public DishEditDialog(Frame parent, DishService dishService, Dish dish) {
        super(parent, dish == null ? "添加菜品" : "编辑菜品", true);
        this.dishService = dishService;
        this.dish = dish;
        this.isEditMode = (dish != null);
        
        initializeComponents();
        if (isEditMode) {
            loadDishData();
        }
        
        setSize(400, 500);
        setLocationRelativeTo(parent);
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        
        // 创建表单面板
        JPanel formPanel = createFormPanel();
        add(formPanel, BorderLayout.CENTER);
        
        // 创建按钮面板
        JPanel buttonPanel = createButtonPanel();
        add(buttonPanel, BorderLayout.SOUTH);
    }
    
    /**
     * 创建表单面板
     */
    private JPanel createFormPanel() {
        JPanel panel = new JPanel(new GridBagLayout());
        panel.setBorder(BorderFactory.createEmptyBorder(20, 20, 20, 20));
        
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);
        gbc.anchor = GridBagConstraints.WEST;
        
        // 菜品ID
        gbc.gridx = 0; gbc.gridy = 0;
        panel.add(new JLabel("菜品ID:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        idField = new JTextField(20);
        idField.setEnabled(!isEditMode); // 编辑模式下ID不可修改
        panel.add(idField, gbc);
        
        // 菜品名称
        gbc.gridx = 0; gbc.gridy = 1; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("菜品名称:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        nameField = new JTextField(20);
        panel.add(nameField, gbc);
        
        // 价格
        gbc.gridx = 0; gbc.gridy = 2; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("价格:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        priceField = new JTextField(20);
        panel.add(priceField, gbc);
        
        // 分类
        gbc.gridx = 0; gbc.gridy = 3; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("分类:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        categoryComboBox = new JComboBox<>(new String[]{"主食", "汤类", "素食", "饮品", "其他"});
        categoryComboBox.setEditable(true);
        panel.add(categoryComboBox, gbc);
        
        // 折扣
        gbc.gridx = 0; gbc.gridy = 4; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("折扣(0.1-1.0):"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.HORIZONTAL;
        discountField = new JTextField("1.0", 20);
        panel.add(discountField, gbc);
        
        // 是否可用
        gbc.gridx = 0; gbc.gridy = 5; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("是否可用:"), gbc);
        gbc.gridx = 1;
        availableCheckBox = new JCheckBox("可用");
        availableCheckBox.setSelected(true);
        panel.add(availableCheckBox, gbc);
        
        // 描述
        gbc.gridx = 0; gbc.gridy = 6; gbc.fill = GridBagConstraints.NONE;
        panel.add(new JLabel("描述:"), gbc);
        gbc.gridx = 1; gbc.fill = GridBagConstraints.BOTH; gbc.weightx = 1.0; gbc.weighty = 1.0;
        descriptionArea = new JTextArea(5, 20);
        descriptionArea.setLineWrap(true);
        descriptionArea.setWrapStyleWord(true);
        JScrollPane scrollPane = new JScrollPane(descriptionArea);
        panel.add(scrollPane, gbc);
        
        return panel;
    }
    
    /**
     * 创建按钮面板
     */
    private JPanel createButtonPanel() {
        JPanel panel = new JPanel(new FlowLayout());
        
        JButton saveButton = new JButton("保存");
        saveButton.addActionListener(new SaveActionListener());
        
        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(e -> dispose());
        
        panel.add(saveButton);
        panel.add(cancelButton);
        
        return panel;
    }
    
    /**
     * 加载菜品数据
     */
    private void loadDishData() {
        if (dish != null) {
            idField.setText(dish.getId());
            nameField.setText(dish.getName());
            priceField.setText(dish.getPrice().toString());
            categoryComboBox.setSelectedItem(dish.getCategory());
            descriptionArea.setText(dish.getDescription());
            availableCheckBox.setSelected(dish.isAvailable());
            discountField.setText(dish.getDiscount().toString());
        }
    }
    
    /**
     * 验证输入数据
     */
    private String validateInput() {
        if (idField.getText().trim().isEmpty()) {
            return "菜品ID不能为空";
        }
        
        if (nameField.getText().trim().isEmpty()) {
            return "菜品名称不能为空";
        }
        
        try {
            BigDecimal price = new BigDecimal(priceField.getText().trim());
            if (price.compareTo(BigDecimal.ZERO) <= 0) {
                return "价格必须大于0";
            }
        } catch (NumberFormatException e) {
            return "价格格式不正确";
        }
        
        try {
            BigDecimal discount = new BigDecimal(discountField.getText().trim());
            if (discount.compareTo(new BigDecimal("0.1")) < 0 || 
                discount.compareTo(BigDecimal.ONE) > 0) {
                return "折扣必须在0.1-1.0之间";
            }
        } catch (NumberFormatException e) {
            return "折扣格式不正确";
        }
        
        return null; // 验证通过
    }
    
    /**
     * 创建或更新菜品对象
     */
    private Dish createDishFromInput() {
        String id = idField.getText().trim();
        String name = nameField.getText().trim();
        BigDecimal price = new BigDecimal(priceField.getText().trim());
        String category = (String) categoryComboBox.getSelectedItem();
        String description = descriptionArea.getText().trim();
        boolean available = availableCheckBox.isSelected();
        BigDecimal discount = new BigDecimal(discountField.getText().trim());
        
        if (isEditMode) {
            // 更新现有菜品
            dish.setName(name);
            dish.setPrice(price);
            dish.setCategory(category);
            dish.setDescription(description);
            dish.setAvailable(available);
            dish.setDiscount(discount);
            return dish;
        } else {
            // 创建新菜品
            return new Dish(id, name, price, category, description, available, discount);
        }
    }
    
    /**
     * 保存按钮事件监听器
     */
    private class SaveActionListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            // 验证输入
            String validationError = validateInput();
            if (validationError != null) {
                JOptionPane.showMessageDialog(DishEditDialog.this, 
                    validationError, "输入错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 创建菜品对象
            Dish dishToSave = createDishFromInput();
            
            // 保存菜品
            boolean success;
            if (isEditMode) {
                success = dishService.updateDish(dishToSave);
            } else {
                success = dishService.addDish(dishToSave);
            }
            
            if (success) {
                confirmed = true;
                dish = dishToSave;
                JOptionPane.showMessageDialog(DishEditDialog.this, 
                    isEditMode ? "菜品更新成功" : "菜品添加成功", 
                    "成功", JOptionPane.INFORMATION_MESSAGE);
                dispose();
            } else {
                JOptionPane.showMessageDialog(DishEditDialog.this, 
                    isEditMode ? "菜品更新失败" : "菜品添加失败", 
                    "错误", JOptionPane.ERROR_MESSAGE);
            }
        }
    }
    
    /**
     * 是否确认保存
     */
    public boolean isConfirmed() {
        return confirmed;
    }
    
    /**
     * 获取菜品对象
     */
    public Dish getDish() {
        return dish;
    }
}
