package com.restaurant.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.restaurant.model.Order;
import com.restaurant.model.DineInOrder;
import com.restaurant.model.TakeawayOrder;
import com.restaurant.util.FileUtil;
import com.restaurant.util.JsonUtil;

import java.io.IOException;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 订单数据访问对象
 * Order Data Access Object
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class OrderDao {
    
    /**
     * 保存订单到今日文件
     * @param order 订单
     * @return 是否保存成功
     */
    public boolean saveOrder(Order order) {
        if (order == null) {
            return false;
        }
        
        try {
            List<Order> todayOrders = getTodayOrders();
            todayOrders.add(order);
            saveTodayOrders(todayOrders);
            return true;
        } catch (Exception e) {
            System.err.println("保存订单失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取今日所有订单
     * @return 今日订单列表
     */
    public List<Order> getTodayOrders() {
        return getOrdersByDate(LocalDate.now());
    }
    
    /**
     * 获取指定日期的订单
     * @param date 日期
     * @return 订单列表
     */
    public List<Order> getOrdersByDate(LocalDate date) {
        try {
            String filePath = FileUtil.getOrdersFileByDate(date);
            if (FileUtil.fileExists(filePath)) {
                return JsonUtil.readFromFile(filePath, new TypeReference<List<Order>>() {});
            }
        } catch (IOException e) {
            System.err.println("读取订单数据失败: " + e.getMessage());
        }
        return new ArrayList<>();
    }
    
    /**
     * 保存今日订单
     * @param orders 订单列表
     */
    private void saveTodayOrders(List<Order> orders) throws IOException {
        FileUtil.initializeDataDirectories();
        String filePath = FileUtil.getTodayOrdersFile();
        JsonUtil.writeToFile(orders, filePath);
    }
    
    /**
     * 根据订单ID查找订单
     * @param orderId 订单ID
     * @return 订单（可能为空）
     */
    public Optional<Order> findById(String orderId) {
        return getTodayOrders().stream()
                .filter(order -> order.getOrderId().equals(orderId))
                .findFirst();
    }
    
    /**
     * 根据手机号查找订单
     * @param phone 手机号
     * @return 订单列表
     */
    public List<Order> findByPhone(String phone) {
        return getTodayOrders().stream()
                .filter(order -> phone.equals(order.getCustomerPhone()))
                .collect(Collectors.toList());
    }
    
    /**
     * 更新订单
     * @param order 订单
     * @return 是否更新成功
     */
    public boolean updateOrder(Order order) {
        if (order == null) {
            return false;
        }
        
        try {
            List<Order> orders = getTodayOrders();
            for (int i = 0; i < orders.size(); i++) {
                if (orders.get(i).getOrderId().equals(order.getOrderId())) {
                    orders.set(i, order);
                    saveTodayOrders(orders);
                    return true;
                }
            }
        } catch (Exception e) {
            System.err.println("更新订单失败: " + e.getMessage());
        }
        return false;
    }
    
    /**
     * 删除订单
     * @param orderId 订单ID
     * @return 是否删除成功
     */
    public boolean deleteOrder(String orderId) {
        try {
            List<Order> orders = getTodayOrders();
            boolean removed = orders.removeIf(order -> order.getOrderId().equals(orderId));
            if (removed) {
                saveTodayOrders(orders);
            }
            return removed;
        } catch (Exception e) {
            System.err.println("删除订单失败: " + e.getMessage());
            return false;
        }
    }
}
