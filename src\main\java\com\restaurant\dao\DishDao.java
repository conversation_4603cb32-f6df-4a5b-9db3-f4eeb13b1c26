package com.restaurant.dao;

import com.fasterxml.jackson.core.type.TypeReference;
import com.restaurant.model.Dish;
import com.restaurant.util.FileUtil;
import com.restaurant.util.JsonUtil;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 菜品数据访问对象
 * Dish Data Access Object
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DishDao {
    
    private List<Dish> dishes;
    
    public DishDao() {
        this.dishes = new ArrayList<>();
        loadDishes();
    }
    
    /**
     * 从文件加载菜品数据
     */
    private void loadDishes() {
        try {
            if (FileUtil.fileExists(FileUtil.MENU_FILE)) {
                List<Dish> loadedDishes = JsonUtil.readFromFile(FileUtil.MENU_FILE, new TypeReference<List<Dish>>() {});
                if (loadedDishes != null) {
                    this.dishes = loadedDishes;
                }
            } else {
                // 如果文件不存在，创建默认菜品
                createDefaultDishes();
                saveDishes();
            }
        } catch (IOException e) {
            System.err.println("加载菜品数据失败: " + e.getMessage());
            createDefaultDishes();
        }
    }
    
    /**
     * 创建默认菜品数据
     */
    private void createDefaultDishes() {
        dishes.clear();
        
        // 主食类
        dishes.add(new Dish("001", "宫保鸡丁", new BigDecimal("28.00"), "主食", "经典川菜，鸡丁配花生米", true, BigDecimal.ONE));
        dishes.add(new Dish("002", "红烧肉", new BigDecimal("35.00"), "主食", "传统红烧肉，肥瘦相间", true, BigDecimal.ONE));
        dishes.add(new Dish("003", "麻婆豆腐", new BigDecimal("18.00"), "主食", "四川名菜，麻辣鲜香", true, BigDecimal.ONE));
        dishes.add(new Dish("004", "糖醋里脊", new BigDecimal("32.00"), "主食", "酸甜可口的经典菜品", true, BigDecimal.ONE));
        
        // 汤类
        dishes.add(new Dish("101", "西红柿鸡蛋汤", new BigDecimal("12.00"), "汤类", "清淡营养的家常汤", true, BigDecimal.ONE));
        dishes.add(new Dish("102", "冬瓜排骨汤", new BigDecimal("25.00"), "汤类", "清热解腻，营养丰富", true, BigDecimal.ONE));
        dishes.add(new Dish("103", "紫菜蛋花汤", new BigDecimal("10.00"), "汤类", "简单清爽的汤品", true, BigDecimal.ONE));
        
        // 素食类
        dishes.add(new Dish("201", "清炒时蔬", new BigDecimal("15.00"), "素食", "当季新鲜蔬菜", true, BigDecimal.ONE));
        dishes.add(new Dish("202", "干煸四季豆", new BigDecimal("20.00"), "素食", "川菜经典素食", true, BigDecimal.ONE));
        dishes.add(new Dish("203", "蒜蓉菠菜", new BigDecimal("12.00"), "素食", "营养丰富的绿叶菜", true, BigDecimal.ONE));
        
        // 饮品类
        dishes.add(new Dish("301", "鲜榨橙汁", new BigDecimal("15.00"), "饮品", "新鲜橙子现榨", true, BigDecimal.ONE));
        dishes.add(new Dish("302", "柠檬蜂蜜茶", new BigDecimal("12.00"), "饮品", "清香解腻", true, BigDecimal.ONE));
        dishes.add(new Dish("303", "绿豆汤", new BigDecimal("8.00"), "饮品", "清热降火", true, BigDecimal.ONE));
    }
    
    /**
     * 保存菜品数据到文件
     */
    public void saveDishes() {
        try {
            FileUtil.initializeDataDirectories();
            JsonUtil.writeToFile(dishes, FileUtil.MENU_FILE);
        } catch (IOException e) {
            System.err.println("保存菜品数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取所有菜品
     * @return 菜品列表
     */
    public List<Dish> getAllDishes() {
        return new ArrayList<>(dishes);
    }
    
    /**
     * 根据ID查找菜品
     * @param id 菜品ID
     * @return 菜品（可能为空）
     */
    public Optional<Dish> findById(String id) {
        return dishes.stream()
                .filter(dish -> dish.getId().equals(id))
                .findFirst();
    }
    
    /**
     * 根据名称查找菜品
     * @param name 菜品名称
     * @return 菜品列表
     */
    public List<Dish> findByName(String name) {
        return dishes.stream()
                .filter(dish -> dish.getName().contains(name))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据分类查找菜品
     * @param category 分类
     * @return 菜品列表
     */
    public List<Dish> findByCategory(String category) {
        return dishes.stream()
                .filter(dish -> dish.getCategory().equals(category))
                .collect(Collectors.toList());
    }
    
    /**
     * 获取所有可用菜品
     * @return 可用菜品列表
     */
    public List<Dish> getAvailableDishes() {
        return dishes.stream()
                .filter(Dish::isAvailable)
                .collect(Collectors.toList());
    }
    
    /**
     * 添加菜品
     * @param dish 菜品
     * @return 是否添加成功
     */
    public boolean addDish(Dish dish) {
        if (dish == null || findById(dish.getId()).isPresent()) {
            return false;
        }
        dishes.add(dish);
        saveDishes();
        return true;
    }
    
    /**
     * 更新菜品
     * @param dish 菜品
     * @return 是否更新成功
     */
    public boolean updateDish(Dish dish) {
        if (dish == null) {
            return false;
        }
        
        for (int i = 0; i < dishes.size(); i++) {
            if (dishes.get(i).getId().equals(dish.getId())) {
                dishes.set(i, dish);
                saveDishes();
                return true;
            }
        }
        return false;
    }
    
    /**
     * 删除菜品
     * @param id 菜品ID
     * @return 是否删除成功
     */
    public boolean deleteDish(String id) {
        boolean removed = dishes.removeIf(dish -> dish.getId().equals(id));
        if (removed) {
            saveDishes();
        }
        return removed;
    }
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    public List<String> getAllCategories() {
        return dishes.stream()
                .map(Dish::getCategory)
                .distinct()
                .collect(Collectors.toList());
    }
}
