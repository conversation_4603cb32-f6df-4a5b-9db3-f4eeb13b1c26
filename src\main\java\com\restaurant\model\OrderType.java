package com.restaurant.model;

/**
 * 订单类型枚举
 * Order Type Enumeration
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public enum OrderType {
    DINE_IN("在店消费"),
    TAKEAWAY("外卖");
    
    private final String description;
    
    OrderType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
