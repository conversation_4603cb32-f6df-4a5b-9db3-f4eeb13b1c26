package com.restaurant.model;

/**
 * 订单类型枚举
 */
public enum OrderType {
    DINE_IN("在店消费"),
    TAKEAWAY("外卖");
    
    private final String description;
    
    OrderType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
