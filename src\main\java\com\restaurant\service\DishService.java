package com.restaurant.service;

import com.restaurant.dao.DishDao;
import com.restaurant.model.Dish;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 菜品管理服务
 * Dish Management Service
 * 
 * <AUTHOR> System
 * @version 1.0
 */
public class DishService {
    
    private final DishDao dishDao;
    
    public DishService() {
        this.dishDao = new DishDao();
    }
    
    /**
     * 获取所有菜品
     * @return 菜品列表
     */
    public List<Dish> getAllDishes() {
        return dishDao.getAllDishes();
    }
    
    /**
     * 获取所有可用菜品
     * @return 可用菜品列表
     */
    public List<Dish> getAvailableDishes() {
        return dishDao.getAvailableDishes();
    }
    
    /**
     * 根据分类获取菜品
     * @param category 分类
     * @return 菜品列表
     */
    public List<Dish> getDishesByCategory(String category) {
        return dishDao.findByCategory(category);
    }
    
    /**
     * 获取所有分类
     * @return 分类列表
     */
    public List<String> getAllCategories() {
        return dishDao.getAllCategories();
    }
    
    /**
     * 根据ID查找菜品
     * @param id 菜品ID
     * @return 菜品（可能为空）
     */
    public Optional<Dish> findDishById(String id) {
        return dishDao.findById(id);
    }
    
    /**
     * 根据名称搜索菜品
     * @param name 菜品名称
     * @return 菜品列表
     */
    public List<Dish> searchDishesByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return getAllDishes();
        }
        return dishDao.findByName(name.trim());
    }
    
    /**
     * 添加菜品
     * @param dish 菜品
     * @return 是否添加成功
     */
    public boolean addDish(Dish dish) {
        if (dish == null || dish.getId() == null || dish.getName() == null || dish.getPrice() == null) {
            return false;
        }
        
        // 检查ID是否已存在
        if (dishDao.findById(dish.getId()).isPresent()) {
            return false;
        }
        
        return dishDao.addDish(dish);
    }
    
    /**
     * 更新菜品
     * @param dish 菜品
     * @return 是否更新成功
     */
    public boolean updateDish(Dish dish) {
        if (dish == null || dish.getId() == null) {
            return false;
        }
        
        // 检查菜品是否存在
        if (!dishDao.findById(dish.getId()).isPresent()) {
            return false;
        }
        
        return dishDao.updateDish(dish);
    }
    
    /**
     * 删除菜品
     * @param id 菜品ID
     * @return 是否删除成功
     */
    public boolean deleteDish(String id) {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }
        
        return dishDao.deleteDish(id);
    }
    
    /**
     * 设置菜品折扣
     * @param id 菜品ID
     * @param discount 折扣（0.1-1.0）
     * @return 是否设置成功
     */
    public boolean setDishDiscount(String id, BigDecimal discount) {
        if (id == null || discount == null || 
            discount.compareTo(BigDecimal.ZERO) <= 0 || 
            discount.compareTo(BigDecimal.ONE) > 0) {
            return false;
        }
        
        Optional<Dish> dishOpt = dishDao.findById(id);
        if (dishOpt.isPresent()) {
            Dish dish = dishOpt.get();
            dish.setDiscount(discount);
            return dishDao.updateDish(dish);
        }
        
        return false;
    }
    
    /**
     * 设置菜品可用性
     * @param id 菜品ID
     * @param available 是否可用
     * @return 是否设置成功
     */
    public boolean setDishAvailability(String id, boolean available) {
        if (id == null) {
            return false;
        }
        
        Optional<Dish> dishOpt = dishDao.findById(id);
        if (dishOpt.isPresent()) {
            Dish dish = dishOpt.get();
            dish.setAvailable(available);
            return dishDao.updateDish(dish);
        }
        
        return false;
    }
    
    /**
     * 验证菜品数据
     * @param dish 菜品
     * @return 验证结果
     */
    public String validateDish(Dish dish) {
        if (dish == null) {
            return "菜品不能为空";
        }
        
        if (dish.getId() == null || dish.getId().trim().isEmpty()) {
            return "菜品ID不能为空";
        }
        
        if (dish.getName() == null || dish.getName().trim().isEmpty()) {
            return "菜品名称不能为空";
        }
        
        if (dish.getPrice() == null || dish.getPrice().compareTo(BigDecimal.ZERO) <= 0) {
            return "菜品价格必须大于0";
        }
        
        if (dish.getCategory() == null || dish.getCategory().trim().isEmpty()) {
            return "菜品分类不能为空";
        }
        
        if (dish.getDiscount() != null && 
            (dish.getDiscount().compareTo(BigDecimal.ZERO) <= 0 || 
             dish.getDiscount().compareTo(BigDecimal.ONE) > 0)) {
            return "折扣必须在0.1-1.0之间";
        }
        
        return null; // 验证通过
    }
}
