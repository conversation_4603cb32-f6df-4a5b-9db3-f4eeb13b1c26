package com.restaurant.util;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import javax.sql.DataSource;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.Properties;

/**
 * 数据库配置管理类
 */
public class DatabaseConfig {
    
    private static DatabaseConfig instance;
    private HikariDataSource dataSource;
    private Properties properties;
    
    private DatabaseConfig() {
        loadProperties();
        initializeDataSource();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized DatabaseConfig getInstance() {
        if (instance == null) {
            instance = new DatabaseConfig();
        }
        return instance;
    }
    
    /**
     * 加载配置文件
     */
    private void loadProperties() {
        properties = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream("database.properties")) {
            if (input != null) {
                properties.load(input);
            } else {
                // 使用默认配置
                setDefaultProperties();
            }
        } catch (IOException e) {
            System.err.println("加载数据库配置文件失败，使用默认配置: " + e.getMessage());
            setDefaultProperties();
        }
    }
    
    /**
     * 设置默认配置
     */
    private void setDefaultProperties() {
        properties.setProperty("mysql.host", "localhost");
        properties.setProperty("mysql.port", "3306");
        properties.setProperty("mysql.database", "restaurant_db");
        properties.setProperty("mysql.username", "root");
        properties.setProperty("mysql.password", "");
        properties.setProperty("connection.pool.initial.size", "5");
        properties.setProperty("connection.pool.max.size", "20");
        properties.setProperty("connection.pool.timeout", "30000");
    }
    
    /**
     * 初始化数据源
     */
    private void initializeDataSource() {
        try {
            HikariConfig config = new HikariConfig();
            
            // 数据库连接配置
            String host = properties.getProperty("mysql.host", "localhost");
            String port = properties.getProperty("mysql.port", "3306");
            String database = properties.getProperty("mysql.database", "restaurant_db");
            String username = properties.getProperty("mysql.username", "root");
            String password = properties.getProperty("mysql.password", "123456");
            
            String jdbcUrl = String.format("*********************************************************************************************************", 
                    host, port, database);
            
            config.setJdbcUrl(jdbcUrl);
            config.setUsername(username);
            config.setPassword(password);
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
            
            // 连接池配置
            config.setMinimumIdle(Integer.parseInt(properties.getProperty("connection.pool.initial.size", "5")));
            config.setMaximumPoolSize(Integer.parseInt(properties.getProperty("connection.pool.max.size", "20")));
            config.setConnectionTimeout(Long.parseLong(properties.getProperty("connection.pool.timeout", "30000")));
            
            // 连接池名称
            config.setPoolName("RestaurantCP");
            
            // 连接测试查询
            config.setConnectionTestQuery("SELECT 1");
            
            // 其他配置
            config.setAutoCommit(true);
            config.setIdleTimeout(600000); // 10分钟
            config.setMaxLifetime(1800000); // 30分钟
            
            dataSource = new HikariDataSource(config);
            
            System.out.println("数据库连接池初始化成功");
            
        } catch (Exception e) {
            System.err.println("数据库连接池初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取数据源
     */
    public DataSource getDataSource() {
        return dataSource;
    }
    
    /**
     * 获取数据库连接
     */
    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("数据源未初始化");
        }
        return dataSource.getConnection();
    }
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        try (Connection conn = getConnection()) {
            return conn != null && !conn.isClosed();
        } catch (SQLException e) {
            System.err.println("数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 初始化数据库表结构
     */
    public void initializeDatabase() {
        try (Connection conn = getConnection();
             InputStream sqlStream = getClass().getClassLoader().getResourceAsStream("sql/init_mysql.sql")) {
            
            if (sqlStream == null) {
                System.err.println("未找到数据库初始化脚本");
                return;
            }
            
            // 读取SQL脚本
            String sqlScript = new String(sqlStream.readAllBytes(), "UTF-8");
            
            // 分割SQL语句并执行
            String[] sqlStatements = sqlScript.split(";");
            
            try (Statement stmt = conn.createStatement()) {
                for (String sql : sqlStatements) {
                    sql = sql.trim();
                    if (!sql.isEmpty() && !sql.startsWith("--")) {
                        stmt.execute(sql);
                    }
                }
            }
            
            System.out.println("数据库表结构初始化成功");
            
        } catch (Exception e) {
            System.err.println("数据库初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 关闭数据源
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            System.out.println("数据库连接池已关闭");
        }
    }
    
    /**
     * 获取配置属性
     */
    public String getProperty(String key) {
        return properties.getProperty(key);
    }
    
    /**
     * 获取配置属性（带默认值）
     */
    public String getProperty(String key, String defaultValue) {
        return properties.getProperty(key, defaultValue);
    }
}
