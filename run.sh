#!/bin/bash

echo "正在启动餐厅自助点餐系统..."
echo

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误：未找到Java环境，请确保已安装Java 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "警告：未找到Maven，尝试直接运行编译后的类文件..."
    if [ -f "target/classes/com/restaurant/RestaurantApplication.class" ]; then
        echo "找到编译后的类文件，正在启动..."
        java -cp "target/classes:target/dependency/*" com.restaurant.RestaurantApplication
    else
        echo "错误：未找到编译后的类文件，请先编译项目"
        echo "请安装Maven并运行: mvn compile"
        exit 1
    fi
else
    echo "正在编译项目..."
    mvn compile
    if [ $? -ne 0 ]; then
        echo "编译失败，请检查代码"
        exit 1
    fi
    
    echo "编译成功，正在启动应用程序..."
    mvn exec:java -Dexec.mainClass="com.restaurant.RestaurantApplication"
fi
