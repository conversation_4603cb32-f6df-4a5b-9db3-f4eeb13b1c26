package com.restaurant.ui;

import java.awt.BorderLayout;
import java.awt.CardLayout;
import java.awt.Color;
import java.awt.Cursor;
import java.awt.FlowLayout;
import java.awt.Font;
import java.awt.GridBagConstraints;
import java.awt.GridBagLayout;
import java.awt.Insets;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import javax.swing.BorderFactory;
import javax.swing.ButtonGroup;
import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JComboBox;
import javax.swing.JLabel;
import javax.swing.JOptionPane;
import javax.swing.JPanel;
import javax.swing.JRadioButton;
import javax.swing.JScrollPane;
import javax.swing.JSpinner;
import javax.swing.JSplitPane;
import javax.swing.JTable;
import javax.swing.JTextField;
import javax.swing.ListSelectionModel;
import javax.swing.SpinnerNumberModel;
import javax.swing.SwingConstants;
import javax.swing.table.DefaultTableModel;

import com.restaurant.model.DineInOrder;
import com.restaurant.model.Dish;
import com.restaurant.model.Order;
import com.restaurant.model.OrderItem;
import com.restaurant.model.TakeawayOrder;
import com.restaurant.service.DishService;
import com.restaurant.service.OrderService;

/**
 * 顾客点餐面板
 */
public class CustomerOrderPanel extends JPanel {
    
    private final MainFrame mainFrame;
    private final DishService dishService;
    private final OrderService orderService;
    
    // UI组件
    private JTable dishTable;
    private DefaultTableModel dishTableModel;
    private JTable orderTable;
    private DefaultTableModel orderTableModel;
    private JComboBox<String> categoryComboBox;
    private JTextField searchField;
    private JLabel totalAmountLabel;
    
    // 订单相关
    private Order currentOrder;
    private JRadioButton dineInRadio;
    private JRadioButton takeawayRadio;
    private JTextField tableNumberField;
    private JCheckBox privateRoomCheckBox;
    private JTextField privateRoomFeeField;
    private JTextField deliveryAddressField;
    private JTextField customerPhoneField;
    private JTextField deliveryFeeField;

    // CardLayout相关
    private CardLayout orderTypeCardLayout;
    private JPanel orderTypeDetailsPanel;
    
    public CustomerOrderPanel(MainFrame mainFrame) {
        this.mainFrame = mainFrame;
        this.dishService = new DishService();
        this.orderService = new OrderService();
        initializeComponents();
        loadDishes();
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        setLayout(new BorderLayout());
        setBackground(new Color(245, 245, 245));
        
        // 创建顶部面板
        add(createTopPanel(), BorderLayout.NORTH);
        
        // 创建中央面板
        add(createCenterPanel(), BorderLayout.CENTER);
        
        // 创建底部面板
        add(createBottomPanel(), BorderLayout.SOUTH);
    }
    
    /**
     * 创建顶部面板
     */
    private JPanel createTopPanel() {
        JPanel topPanel = new JPanel(new BorderLayout());
        topPanel.setBackground(new Color(52, 152, 219));
        topPanel.setBorder(BorderFactory.createEmptyBorder(10, 15, 10, 15));
        
        // 标题
        JLabel titleLabel = new JLabel("顾客点餐");
        titleLabel.setFont(new Font("微软雅黑", Font.BOLD, 20));
        titleLabel.setForeground(Color.WHITE);
        
        // 返回按钮
        JButton backButton = new JButton("返回主页");
        backButton.setFont(new Font("微软雅黑", Font.PLAIN, 14));
        backButton.setBackground(new Color(231, 76, 60));
        backButton.setForeground(Color.WHITE);
        backButton.setFocusPainted(false);
        backButton.setBorderPainted(false);
        backButton.setCursor(new Cursor(Cursor.HAND_CURSOR));
        backButton.addActionListener(e -> {
            if (confirmBackToMain()) {
                mainFrame.showWelcomePanel();
            }
        });
        
        topPanel.add(titleLabel, BorderLayout.WEST);
        topPanel.add(backButton, BorderLayout.EAST);
        
        return topPanel;
    }
    
    /**
     * 创建中央面板
     */
    private JPanel createCenterPanel() {
        JPanel centerPanel = new JPanel(new BorderLayout());
        centerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // 创建分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.HORIZONTAL_SPLIT);
        splitPane.setDividerLocation(600);
        splitPane.setResizeWeight(0.6);
        
        // 左侧：菜品选择面板
        splitPane.setLeftComponent(createDishSelectionPanel());
        
        // 右侧：订单详情面板
        splitPane.setRightComponent(createOrderDetailsPanel());
        
        centerPanel.add(splitPane, BorderLayout.CENTER);
        
        return centerPanel;
    }
    
    /**
     * 创建菜品选择面板
     */
    private JPanel createDishSelectionPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("菜品选择"));
        
        // 搜索面板
        JPanel searchPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        
        searchPanel.add(new JLabel("分类:"));
        categoryComboBox = new JComboBox<>();
        categoryComboBox.addActionListener(e -> filterDishes());
        searchPanel.add(categoryComboBox);
        
        searchPanel.add(new JLabel("搜索:"));
        searchField = new JTextField(15);
        searchField.addActionListener(e -> filterDishes());
        searchPanel.add(searchField);
        
        JButton searchButton = new JButton("搜索");
        searchButton.addActionListener(e -> filterDishes());
        searchPanel.add(searchButton);
        
        panel.add(searchPanel, BorderLayout.NORTH);
        
        // 菜品表格
        String[] dishColumns = {"菜品ID", "菜品名称", "分类", "原价", "折扣", "现价", "描述", "状态"};
        dishTableModel = new DefaultTableModel(dishColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        dishTable = new JTable(dishTableModel);
        dishTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        dishTable.getTableHeader().setReorderingAllowed(false);
        
        JScrollPane dishScrollPane = new JScrollPane(dishTable);
        panel.add(dishScrollPane, BorderLayout.CENTER);
        
        // 添加到订单按钮
        JPanel addPanel = new JPanel(new FlowLayout());
        JLabel quantityLabel = new JLabel("数量:");
        JSpinner quantitySpinner = new JSpinner(new SpinnerNumberModel(1, 1, 99, 1));
        JButton addButton = new JButton("添加到订单");
        addButton.addActionListener(e -> addDishToOrder(quantitySpinner));
        
        addPanel.add(quantityLabel);
        addPanel.add(quantitySpinner);
        addPanel.add(addButton);
        panel.add(addPanel, BorderLayout.SOUTH);
        
        return panel;
    }
    
    /**
     * 创建订单详情面板
     */
    private JPanel createOrderDetailsPanel() {
        JPanel panel = new JPanel(new BorderLayout());
        panel.setBorder(BorderFactory.createTitledBorder("订单详情"));
        
        // 订单类型选择
        JPanel typePanel = createOrderTypePanel();
        panel.add(typePanel, BorderLayout.NORTH);
        
        // 订单项表格
        String[] orderColumns = {"菜品名称", "单价", "数量", "小计"};
        orderTableModel = new DefaultTableModel(orderColumns, 0) {
            @Override
            public boolean isCellEditable(int row, int column) {
                return false;
            }
        };
        orderTable = new JTable(orderTableModel);
        orderTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        
        JScrollPane orderScrollPane = new JScrollPane(orderTable);
        panel.add(orderScrollPane, BorderLayout.CENTER);
        
        // 底部操作面板
        JPanel bottomPanel = new JPanel(new BorderLayout());
        
        // 总金额显示
        totalAmountLabel = new JLabel("总金额: ¥0.00");
        totalAmountLabel.setFont(new Font("微软雅黑", Font.BOLD, 16));
        totalAmountLabel.setHorizontalAlignment(SwingConstants.RIGHT);
        bottomPanel.add(totalAmountLabel, BorderLayout.NORTH);
        
        // 操作按钮
        JPanel buttonPanel = new JPanel(new FlowLayout());
        JButton removeButton = new JButton("移除选中");
        removeButton.addActionListener(e -> removeSelectedOrderItem());
        JButton clearButton = new JButton("清空订单");
        clearButton.addActionListener(e -> clearOrder());
        JButton submitButton = new JButton("提交订单");
        submitButton.addActionListener(e -> submitOrder());
        
        buttonPanel.add(removeButton);
        buttonPanel.add(clearButton);
        buttonPanel.add(submitButton);
        bottomPanel.add(buttonPanel, BorderLayout.SOUTH);
        
        panel.add(bottomPanel, BorderLayout.SOUTH);
        
        return panel;
    }

    /**
     * 创建订单类型面板
     */
    private JPanel createOrderTypePanel() {
        JPanel panel = new JPanel(new BorderLayout());

        // 订单类型选择
        JPanel typeSelectionPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        ButtonGroup typeGroup = new ButtonGroup();

        dineInRadio = new JRadioButton("在店消费", true);
        takeawayRadio = new JRadioButton("外卖");
        typeGroup.add(dineInRadio);
        typeGroup.add(takeawayRadio);

        dineInRadio.addActionListener(e -> updateOrderTypeFields());
        takeawayRadio.addActionListener(e -> updateOrderTypeFields());

        typeSelectionPanel.add(dineInRadio);
        typeSelectionPanel.add(takeawayRadio);
        panel.add(typeSelectionPanel, BorderLayout.NORTH);

        // 订单详细信息面板
        orderTypeCardLayout = new CardLayout();
        orderTypeDetailsPanel = new JPanel(orderTypeCardLayout);

        // 在店消费详情
        JPanel dineInPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        gbc.insets = new Insets(5, 5, 5, 5);

        gbc.gridx = 0; gbc.gridy = 0;
        dineInPanel.add(new JLabel("餐桌号:"), gbc);
        gbc.gridx = 1;
        tableNumberField = new JTextField(10);
        dineInPanel.add(tableNumberField, gbc);

        gbc.gridx = 0; gbc.gridy = 1;
        privateRoomCheckBox = new JCheckBox("包厢");
        dineInPanel.add(privateRoomCheckBox, gbc);
        gbc.gridx = 1;
        privateRoomFeeField = new JTextField("0.00", 10);
        privateRoomFeeField.setEnabled(false);
        dineInPanel.add(privateRoomFeeField, gbc);

        privateRoomCheckBox.addActionListener(e -> {
            privateRoomFeeField.setEnabled(privateRoomCheckBox.isSelected());
            if (!privateRoomCheckBox.isSelected()) {
                privateRoomFeeField.setText("0.00");
            }
        });

        // 外卖详情
        JPanel takeawayPanel = new JPanel(new GridBagLayout());
        gbc.gridx = 0; gbc.gridy = 0;
        takeawayPanel.add(new JLabel("送餐地址:"), gbc);
        gbc.gridx = 1;
        deliveryAddressField = new JTextField(15);
        takeawayPanel.add(deliveryAddressField, gbc);

        gbc.gridx = 0; gbc.gridy = 1;
        takeawayPanel.add(new JLabel("联系电话:"), gbc);
        gbc.gridx = 1;
        customerPhoneField = new JTextField(15);
        takeawayPanel.add(customerPhoneField, gbc);

        gbc.gridx = 0; gbc.gridy = 2;
        takeawayPanel.add(new JLabel("外卖费:"), gbc);
        gbc.gridx = 1;
        deliveryFeeField = new JTextField("5.00", 10);
        takeawayPanel.add(deliveryFeeField, gbc);

        orderTypeDetailsPanel.add(dineInPanel, "DINE_IN");
        orderTypeDetailsPanel.add(takeawayPanel, "TAKEAWAY");
        panel.add(orderTypeDetailsPanel, BorderLayout.CENTER);

        return panel;
    }

    /**
     * 创建底部面板
     */
    private JPanel createBottomPanel() {
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.CENTER));
        bottomPanel.setBackground(new Color(245, 245, 245));
        bottomPanel.setBorder(BorderFactory.createEmptyBorder(5, 0, 5, 0));

        JLabel statusLabel = new JLabel("欢迎使用餐厅自助点餐系统");
        statusLabel.setFont(new Font("微软雅黑", Font.PLAIN, 12));
        statusLabel.setForeground(new Color(102, 102, 102));
        bottomPanel.add(statusLabel);

        return bottomPanel;
    }

    /**
     * 加载菜品数据
     */
    private void loadDishes() {
        // 加载分类
        categoryComboBox.removeAllItems();
        categoryComboBox.addItem("全部");
        List<String> categories = dishService.getAllCategories();
        for (String category : categories) {
            categoryComboBox.addItem(category);
        }

        // 加载菜品
        refreshDishTable();
    }

    /**
     * 刷新菜品表格
     */
    private void refreshDishTable() {
        dishTableModel.setRowCount(0);
        List<Dish> dishes = dishService.getAvailableDishes();

        for (Dish dish : dishes) {
            Object[] row = {
                dish.getId(),
                dish.getName(),
                dish.getCategory(),
                "¥" + dish.getPrice(),
                dish.getDiscount().toString(),
                "¥" + dish.getDiscountedPrice(),
                dish.getDescription(),
                dish.isAvailable() ? "可用" : "不可用"
            };
            dishTableModel.addRow(row);
        }
    }

    /**
     * 过滤菜品
     */
    private void filterDishes() {
        dishTableModel.setRowCount(0);

        String selectedCategory = (String) categoryComboBox.getSelectedItem();
        String searchText = searchField.getText().trim();

        List<Dish> dishes;
        if ("全部".equals(selectedCategory)) {
            dishes = dishService.getAvailableDishes();
        } else {
            dishes = dishService.getDishesByCategory(selectedCategory);
        }

        // 应用搜索过滤
        if (!searchText.isEmpty()) {
            dishes = dishes.stream()
                    .filter(dish -> dish.getName().contains(searchText) ||
                                   dish.getDescription().contains(searchText))
                    .toList();
        }

        for (Dish dish : dishes) {
            if (dish.isAvailable()) {
                Object[] row = {
                    dish.getId(),
                    dish.getName(),
                    dish.getCategory(),
                    "¥" + dish.getPrice(),
                    dish.getDiscount().toString(),
                    "¥" + dish.getDiscountedPrice(),
                    dish.getDescription(),
                    "可用"
                };
                dishTableModel.addRow(row);
            }
        }
    }

    /**
     * 更新订单类型字段
     */
    private void updateOrderTypeFields() {
        // 使用保存的CardLayout引用切换面板
        if (orderTypeCardLayout != null && orderTypeDetailsPanel != null) {
            if (dineInRadio.isSelected()) {
                orderTypeCardLayout.show(orderTypeDetailsPanel, "DINE_IN");
            } else if (takeawayRadio.isSelected()) {
                orderTypeCardLayout.show(orderTypeDetailsPanel, "TAKEAWAY");
            }
        }
    }

    /**
     * 添加菜品到订单
     */
    private void addDishToOrder(JSpinner quantitySpinner) {
        int selectedRow = dishTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择一个菜品", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        String dishId = (String) dishTableModel.getValueAt(selectedRow, 0);
        int quantity = (Integer) quantitySpinner.getValue();

        if (currentOrder == null) {
            createNewOrder();
        }

        if (orderService.addOrderItem(currentOrder, dishId, quantity, "")) {
            refreshOrderTable();
            updateTotalAmount();
            JOptionPane.showMessageDialog(this, "菜品已添加到订单", "成功", JOptionPane.INFORMATION_MESSAGE);
        } else {
            JOptionPane.showMessageDialog(this, "添加菜品失败", "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 创建新订单
     */
    private void createNewOrder() {
        if (dineInRadio.isSelected()) {
            currentOrder = orderService.createDineInOrder("", false, BigDecimal.ZERO, "");
        } else {
            currentOrder = orderService.createTakeawayOrder("", "", LocalDateTime.now(), BigDecimal.ZERO);
        }
    }

    /**
     * 刷新订单表格
     */
    private void refreshOrderTable() {
        orderTableModel.setRowCount(0);
        if (currentOrder != null) {
            for (OrderItem item : currentOrder.getOrderItems()) {
                Object[] row = {
                    item.getDish().getName(),
                    "¥" + item.getUnitPrice(),
                    item.getQuantity(),
                    "¥" + item.getTotalPrice()
                };
                orderTableModel.addRow(row);
            }
        }
    }

    /**
     * 更新总金额
     */
    private void updateTotalAmount() {
        if (currentOrder != null) {
            currentOrder.calculateTotalAmount();
            totalAmountLabel.setText("总金额: ¥" + currentOrder.getTotalAmount());
        } else {
            totalAmountLabel.setText("总金额: ¥0.00");
        }
    }

    /**
     * 移除选中的订单项
     */
    private void removeSelectedOrderItem() {
        int selectedRow = orderTable.getSelectedRow();
        if (selectedRow == -1) {
            JOptionPane.showMessageDialog(this, "请先选择要移除的订单项", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        if (currentOrder != null && selectedRow < currentOrder.getOrderItems().size()) {
            OrderItem itemToRemove = currentOrder.getOrderItems().get(selectedRow);
            currentOrder.removeOrderItem(itemToRemove);
            refreshOrderTable();
            updateTotalAmount();
        }
    }

    /**
     * 清空订单
     */
    private void clearOrder() {
        if (currentOrder != null && !currentOrder.getOrderItems().isEmpty()) {
            int result = JOptionPane.showConfirmDialog(this,
                "确定要清空当前订单吗？", "确认清空",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            if (result == JOptionPane.YES_OPTION) {
                currentOrder = null;
                refreshOrderTable();
                updateTotalAmount();
            }
        }
    }

    /**
     * 提交订单
     */
    private void submitOrder() {
        if (currentOrder == null || currentOrder.getOrderItems().isEmpty()) {
            JOptionPane.showMessageDialog(this, "订单为空，请先添加菜品", "提示", JOptionPane.WARNING_MESSAGE);
            return;
        }

        // 验证订单信息
        String validationError = validateOrderInfo();
        if (validationError != null) {
            JOptionPane.showMessageDialog(this, validationError, "验证失败", JOptionPane.ERROR_MESSAGE);
            return;
        }

        // 设置订单信息
        setOrderInfo();

        // 保存订单
        if (orderService.saveOrder(currentOrder)) {
            String orderDetails = currentOrder.getOrderDetails();
            JOptionPane.showMessageDialog(this, "订单提交成功！\n\n" + orderDetails,
                "订单成功", JOptionPane.INFORMATION_MESSAGE);

            // 清空当前订单
            currentOrder = null;
            refreshOrderTable();
            updateTotalAmount();
            clearOrderFields();
        } else {
            JOptionPane.showMessageDialog(this, "订单提交失败，请重试", "错误", JOptionPane.ERROR_MESSAGE);
        }
    }

    /**
     * 验证订单信息
     */
    private String validateOrderInfo() {
        if (dineInRadio.isSelected()) {
            if (tableNumberField.getText().trim().isEmpty()) {
                return "请输入餐桌号";
            }
        } else {
            if (deliveryAddressField.getText().trim().isEmpty()) {
                return "请输入送餐地址";
            }
            if (customerPhoneField.getText().trim().isEmpty()) {
                return "请输入联系电话";
            }
        }
        return null;
    }

    /**
     * 设置订单信息
     */
    private void setOrderInfo() {
        if (dineInRadio.isSelected() && currentOrder instanceof DineInOrder) {
            DineInOrder dineInOrder = (DineInOrder) currentOrder;
            dineInOrder.setTableNumber(tableNumberField.getText().trim());
            dineInOrder.setHasPrivateRoom(privateRoomCheckBox.isSelected());
            if (privateRoomCheckBox.isSelected()) {
                try {
                    BigDecimal fee = new BigDecimal(privateRoomFeeField.getText());
                    dineInOrder.setPrivateRoomFee(fee);
                } catch (NumberFormatException e) {
                    dineInOrder.setPrivateRoomFee(BigDecimal.ZERO);
                }
            }
        } else if (takeawayRadio.isSelected() && currentOrder instanceof TakeawayOrder) {
            TakeawayOrder takeawayOrder = (TakeawayOrder) currentOrder;
            takeawayOrder.setDeliveryAddress(deliveryAddressField.getText().trim());
            takeawayOrder.setCustomerPhone(customerPhoneField.getText().trim());
            try {
                BigDecimal fee = new BigDecimal(deliveryFeeField.getText());
                takeawayOrder.setDeliveryFee(fee);
            } catch (NumberFormatException e) {
                takeawayOrder.setDeliveryFee(new BigDecimal("5.00"));
            }
        }
    }

    /**
     * 清空订单字段
     */
    private void clearOrderFields() {
        tableNumberField.setText("");
        privateRoomCheckBox.setSelected(false);
        privateRoomFeeField.setText("0.00");
        privateRoomFeeField.setEnabled(false);
        deliveryAddressField.setText("");
        customerPhoneField.setText("");
        deliveryFeeField.setText("5.00");
    }

    /**
     * 确认返回主页
     */
    private boolean confirmBackToMain() {
        if (currentOrder != null && !currentOrder.getOrderItems().isEmpty()) {
            int result = JOptionPane.showConfirmDialog(this,
                "当前有未提交的订单，确定要返回主页吗？", "确认返回",
                JOptionPane.YES_NO_OPTION, JOptionPane.QUESTION_MESSAGE);
            return result == JOptionPane.YES_OPTION;
        }
        return true;
    }
}
